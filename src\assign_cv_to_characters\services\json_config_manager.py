import json
import os
from typing import Any, Dict, Optional
from pathlib import Path

class JsonConfigManager:
    """JSON配置管理类，用于读取和管理JSON格式的配置文件"""

    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化JSON配置管理器

        Args:
            config_file: 配置文件路径，默认为config/config.json
        """
        self.config_file = config_file
        self.config = {}

        # 检查配置文件是否存在，不存在则创建默认配置
        if not os.path.exists(config_file):
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            self._create_default_config()
        else:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except json.JSONDecodeError:
                # 如果JSON解析失败，创建默认配置
                self._create_default_config()

    def _create_default_config(self) -> None:
        """创建默认配置文件"""
        self.config = {
            'API': {
                'base_url': 'https://www.gstudios.com.cn/story_v2/api',
                'default_token': '',
                'enable_real_assignment': 'false'  # 默认禁用真实API调用
            },
            'BOOK': {
                'default_book_id': '33524'
            },
            'EXCEL': {
                'character_sheet_name': '角色',
                'character_column_index': '0',
                'cv_column_index': '6',
                'price_column_index': '2',
                'character_column_keyword': '角色名称',
                'cv_column_keyword': '主播',
                'price_column_keyword': '主播价格',
                'header_row': '1'
            },
            'FILES': {
                'cv_config_file': 'config/cv_nicknames.json',
                'last_excel_directory': ''
            }
        }

        # 保存默认配置
        self.save()

    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """
        获取配置项值

        Args:
            section: 配置节
            key: 配置键
            fallback: 默认值

        Returns:
            配置项的值
        """
        try:
            return str(self.config.get(section, {}).get(key, fallback))
        except (KeyError, TypeError):
            return str(fallback) if fallback is not None else ""

    def set(self, section: str, key: str, value: str) -> None:
        """
        设置配置项值

        Args:
            section: 配置节
            key: 配置键
            value: 配置值
        """
        if section not in self.config:
            self.config[section] = {}

        self.config[section][key] = value

    def save(self) -> None:
        """保存配置到文件"""
        # 确保配置目录存在
        config_dir = os.path.dirname(os.path.abspath(self.config_file))
        if config_dir:
            os.makedirs(config_dir, exist_ok=True)

        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)

    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置

        Returns:
            Dict[str, Any]: 完整的配置字典
        """
        return self.config.copy()

    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新配置

        Args:
            new_config: 新的配置字典
        """
        self.config.update(new_config)
        self.save()

# 创建全局配置管理器实例
json_config_manager = JsonConfigManager("config/config.json")
