/*
 * Settings Panel - 设置面板样式
 * 包含设置页面的所有样式：设置头部、标签页、设置区块、表单控件等
 * 提供应用设置和配置管理的界面样式
 */

/* ==================== 设置页面样式 ==================== */
.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
}

.settings-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.settings-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.settings-actions .btn {
    margin-right: 0;
}

/* 设置TAB导航样式 */
.settings-tabs {
    display: flex;
    background: var(--bg-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-glass);
    box-shadow: var(--shadow-sm);
}

.settings-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    font-weight: 500;
    color: var(--text-secondary);
    position: relative;
    background: transparent;
}

.settings-tab:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.settings-tab.active {
    color: var(--text-inverse);
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.settings-tab .tab-icon {
    font-size: 1.1rem;
}

.settings-tab .tab-text {
    font-size: 0.875rem;
    font-weight: 600;
}

/* 设置TAB内容区域样式 */
.settings-tab-content {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.settings-tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    z-index: 1;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-sm);
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    background: transparent;
    color: inherit;
    height: 100%;
    width: 100%;
}

.settings-tab-pane.active {
    opacity: 1;
    visibility: visible;
    z-index: 2;
}

/* Webkit浏览器滚动条样式 */
.settings-tab-pane::-webkit-scrollbar {
    width: 8px;
}

.settings-tab-pane::-webkit-scrollbar-track {
    background: transparent;
}

.settings-tab-pane::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.settings-tab-pane::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 设置区块样式 */
.settings-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-smooth);
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
}

.settings-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.settings-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 700;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
    position: relative;
}

.settings-section h4::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30%;
    height: 2px;
    background: var(--primary-gradient);
}

.settings-section .form-group {
    margin-bottom: var(--spacing-md);
}

.settings-section .form-group:last-child {
    margin-bottom: 0;
}

.settings-section input[type="text"],
.settings-section input[type="password"],
.settings-section input[type="number"] {
    width: 300px;
    margin-right: var(--spacing-sm);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    transition: all var(--transition-smooth);
    position: relative;
    z-index: 1;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--text-primary);
}

.settings-section input[type="text"]:focus,
.settings-section input[type="password"]:focus,
.settings-section input[type="number"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-glass-hover);
}

.settings-section small {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}
