/*
 * Assignment Panel - 分配任务面板样式
 * 包含分配任务页面的所有样式：面板容器、头部、配置区域、内容区域等
 * 提供分配任务功能的界面样式
 */

/* ==================== 分配任务页面样式 ==================== */
.assignment-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.assignment-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.assignment-config-section {
    flex-shrink: 0;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
}

.assignment-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    width: 100%;
}

.assignment-content-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    padding: var(--spacing-sm);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.assignment-content-wrapper::-webkit-scrollbar {
    width: 8px;
}

.assignment-content-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}
