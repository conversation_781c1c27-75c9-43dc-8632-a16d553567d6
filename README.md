# 角色CV分配工具 Template

基于 [Augment-Code-Free](https://github.com/vagmr/Augment-free) 架构的现代化桌面应用模板。

## 🚀 特性

- **现代化界面**: 基于 HTML/CSS/JavaScript 的现代化用户界面
- **跨平台支持**: 支持 Windows、macOS 和 Linux
- **前后端分离**: 清晰的架构设计，Python 后端 + Web 前端
- **模块化设计**: 高内聚、低耦合的代码组织
- **完整的工具链**: 包含构建、测试、打包等完整工具
- **丰富的功能**: 配置管理、系统信息、错误处理等

## 📋 系统要求

- Python 3.10 或更高版本
- 支持的操作系统：Windows 10+、macOS 10.14+、Linux (Ubuntu 18.04+)

## 🛠️ 快速开始

### 1. 克隆模板

```bash
# 复制模板到您的项目目录
cp -r templates/assign-cv-to-characters/ my-awesome-app/
cd my-awesome-app/
```

### 2. 自定义项目信息

编辑以下文件来自定义您的项目：

- `pyproject.toml`: 修改项目名称、版本、作者等信息
- `src/assign_cv_to_characters/`: 重命名为您的应用名称
- `src/assign_cv_to_characters/__init__.py`: 更新版本和作者信息
- `src/assign_cv_to_characters/web/index.html`: 修改应用标题和描述

### 3. 安装依赖

```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 或者安装开发依赖
pip install -e .[dev]
```

### 4. 运行应用

```bash
# 直接运行
python -m assign_cv_to_characters

# 或者使用入口点
assign-cv-to-characters
```

## 🏗️ 项目结构

```
assign-cv-to-characters/
├── src/assign_cv_to_characters/          # 主要源代码
│   ├── __init__.py             # 包初始化
│   ├── main.py                 # 应用入口点
│   ├── api/                    # 后端API层
│   │   ├── core.py            # 核心API类
│   │   └── handlers/          # 业务处理器
│   ├── utils/                  # 工具模块
│   │   ├── paths.py           # 跨平台路径处理
│   │   └── helpers.py         # 辅助函数
│   └── web/                    # 前端资源
│       ├── index.html         # 主页面
│       ├── css/style.css      # 样式文件
│       └── js/app.js          # JavaScript逻辑
├── scripts/                    # 构建和工具脚本
│   └── build.py               # 构建脚本
├── tests/                      # 测试文件
├── docs/                       # 文档
├── pyproject.toml             # 项目配置
├── requirements.txt           # 依赖列表
├── app.ico                    # 应用图标
└── README.md                  # 项目说明
```

## 🔧 开发指南

### 添加新功能

1. **创建处理器**: 在 `src/assign_cv_to_characters/api/handlers/` 中创建新的处理器文件
2. **添加API方法**: 在 `src/assign_cv_to_characters/api/core.py` 中添加对应的API方法
3. **更新前端**: 在 `src/assign_cv_to_characters/web/js/app.js` 中添加前端调用逻辑
4. **更新界面**: 在 `src/assign_cv_to_characters/web/index.html` 中添加UI元素

### 示例：添加文件操作功能

1. 创建 `src/assign_cv_to_characters/api/handlers/file_handler.py`:

```python
def read_file(file_path: str) -> Dict[str, Any]:
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return {
            "success": True,
            "data": {"content": content, "path": file_path},
            "message": "文件读取成功"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "文件读取失败"
        }
```

2. 在 `core.py` 中添加API方法:

```python
def read_file(self, file_path: str) -> Dict[str, Any]:
    """读取文件API方法"""
    from .handlers.file_handler import read_file
    return read_file(file_path)
```

3. 在前端调用:

```javascript
async function readFile(filePath) {
    const result = await callAPI('read_file', filePath);
    if (result.success) {
        console.log('文件内容:', result.data.content);
    }
}
```

### 配置管理

应用使用JSON格式的配置文件，支持：

- 自动创建默认配置
- 配置验证和合并
- 配置备份和恢复
- 运行时配置更新

配置文件位置：
- Windows: `%APPDATA%/assign_cv_to_characters/config.json`
- macOS: `~/Library/Application Support/assign_cv_to_characters/config.json`
- Linux: `~/.config/assign_cv_to_characters/config.json`

### 错误处理

所有API方法都使用统一的返回格式：

```python
{
    "success": bool,        # 操作是否成功
    "data": any,           # 返回数据（成功时）
    "error": str,          # 错误信息（失败时）
    "message": str,        # 用户友好的消息
    "timestamp": str       # 时间戳
}
```

## 📦 构建和打包

### 开发模式构建

```bash
python scripts/build.py --debug
```

### 生产模式构建

```bash
python scripts/build.py
```

### 清理构建文件

```bash
python scripts/build.py --clean
```

构建完成后，可执行文件将位于 `dist/` 目录中，发布版本将复制到 `release/` 目录。

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src/assign_cv_to_characters --cov-report=html

# 运行特定测试
pytest tests/test_api.py
```

## 📚 API 文档

### 核心API方法

- `get_status()`: 获取API状态
- `get_app_info()`: 获取应用信息
- `get_system_info()`: 获取系统信息
- `get_config()`: 获取当前配置
- `update_config(config)`: 更新配置
- `reset_config()`: 重置配置
- `example_operation(params)`: 示例操作

### 工具函数

- `format_success_response()`: 格式化成功响应
- `format_error_response()`: 格式化错误响应
- `validate_params()`: 参数验证
- `get_home_dir()`: 获取用户主目录
- `get_config_dir()`: 获取配置目录

## 🎨 自定义样式

应用使用CSS变量系统，可以轻松自定义主题：

```css
:root {
    --primary-color: #2563eb;      /* 主色调 */
    --secondary-color: #64748b;    /* 次要色调 */
    --success-color: #059669;      /* 成功色 */
    --error-color: #dc2626;        /* 错误色 */
    /* ... 更多变量 */
}
```

## 🔍 调试

### 开发者工具

在开发环境中，可以通过浏览器开发者工具访问调试对象：

```javascript
// 在浏览器控制台中
window.appDebug.state        // 应用状态
window.appDebug.elements     // DOM元素引用
window.appDebug.callAPI      // API调用函数
```

### 日志记录

应用支持多级别日志记录：

```python
import logging
logger = logging.getLogger(__name__)

logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 基于 [Augment-Code-Free](https://github.com/vagmr/Augment-free) 架构
- 使用 [pywebview](https://pywebview.flowrl.com/) 构建GUI
- 图标来自 [Emoji](https://emojipedia.org/)

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](https://github.com/cv-assignment-team/assign-cv-to-characters/issues)
3. 创建新的 Issue

---

**Happy Coding! 🎉**
