/*
 * CSS Variables - 全局变量定义
 * 包含颜色、间距、阴影、过渡效果等所有CSS变量
 * 为整个应用提供统一的设计令牌
 */

/* ==================== CSS变量 ==================== */
:root {
    /* 现代渐变色彩 - 基于web/目录的设计风格 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-hover: #5a67d8;
    --primary-purple: #764ba2;
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --success-color: #48bb78;
    --success-hover: #38a169;
    --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --warning-color: #d97706;
    --error-color: #dc2626;
    --info-color: #0891b2;

    /* 玻璃拟态背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-glass-secondary: rgba(248, 250, 252, 0.8);
    --bg-glass-hover: rgba(240, 248, 255, 0.95);

    /* 文本颜色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #4a5568;
    --text-inverse: #ffffff;

    /* 带玻璃效果的边框颜色 */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;
    --border-glass: rgba(255, 255, 255, 0.2);
    --border-glass-hover: rgba(102, 126, 234, 0.3);

    /* 增强的玻璃效果阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-glass-hover: 0 12px 30px rgba(102, 126, 234, 0.15);
    --shadow-button: 0 4px 15px rgba(102, 126, 234, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(102, 126, 234, 0.4);

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 增强的边框圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-glass: 1rem;
    --radius-button: 0.5rem;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* 增强的三次贝塞尔过渡效果 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* 玻璃效果滤镜 */
    --glass-blur: blur(20px);
    --glass-blur-light: blur(8px);

    /* Z-Index层级管理 */
    --z-dropdown: 10;
    --z-header: 900;
    --z-sidebar: 800;
    --z-sidebar-overlay: 1050;
    --z-sidebar-mobile: 1100;
    --z-modal-backdrop: 1050;
    --z-modal: 1100;
    --z-tooltip: 1200;
}
