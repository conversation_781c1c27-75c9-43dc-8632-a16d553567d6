"""
Modern GUI App Template - Utilities Module

工具模块，提供各种通用的工具函数和类。
基于 Augment-Code-Free 的工具设计模式。

模块结构：
- paths.py: 跨平台路径处理工具
- helpers.py: 通用辅助函数
- config.py: 配置相关工具（可选）
- logger.py: 日志记录工具（可选）

设计原则：
1. 跨平台兼容性
2. 函数式编程风格
3. 无副作用的纯函数
4. 完善的错误处理

使用方法：
    from modern_gui_app.utils.paths import get_home_dir
    from modern_gui_app.utils.helpers import format_success_response
    
    home = get_home_dir()
    response = format_success_response(data={"home": home})
"""

# 导入主要的工具函数
from .paths import (
    get_home_dir,
    get_app_data_dir,
    get_config_dir,
    get_temp_dir,
    ensure_dir_exists,
)

from .helpers import (
    format_success_response,
    format_error_response,
    validate_params,
    safe_json_loads,
    safe_json_dumps,
)

# 可选的工具模块（根据需要取消注释）
# from .logger import setup_logger, get_logger
# from .config import ConfigManager

# 导出所有工具函数
__all__ = [
    # 路径工具
    "get_home_dir",
    "get_app_data_dir", 
    "get_config_dir",
    "get_temp_dir",
    "ensure_dir_exists",
    
    # 辅助函数
    "format_success_response",
    "format_error_response",
    "validate_params",
    "safe_json_loads",
    "safe_json_dumps",
    
    # 可选工具（根据需要取消注释）
    # "setup_logger",
    # "get_logger",
    # "ConfigManager",
]
