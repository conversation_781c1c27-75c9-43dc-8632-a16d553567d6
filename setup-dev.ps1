# Modern GUI App Template - Development Environment Setup (Windows)
# 基于UV包管理器的Windows开发环境快速设置脚本

param(
    [switch]$Force,
    [switch]$Help
)

# 错误处理
$ErrorActionPreference = "Stop"

# 显示帮助信息
if ($Help) {
    Write-Host @"
Modern GUI App - Windows开发环境设置脚本

用法:
    .\setup-dev.ps1 [选项]

选项:
    -Force    强制重新安装所有组件
    -Help     显示此帮助信息

示例:
    .\setup-dev.ps1           # 标准安装
    .\setup-dev.ps1 -Force    # 强制重新安装
"@
    exit 0
}

# 颜色函数
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Write-Success { param([string]$Text) Write-ColorText "✅ $Text" "Green" }
function Write-Warning { param([string]$Text) Write-ColorText "⚠️  $Text" "Yellow" }
function Write-Error { param([string]$Text) Write-ColorText "❌ $Text" "Red" }
function Write-Info { param([string]$Text) Write-ColorText "📋 $Text" "Cyan" }
function Write-Step { param([string]$Text) Write-ColorText "🔄 $Text" "Blue" }

# 主标题
Write-ColorText @"
🚀 Modern GUI App - Windows开发环境设置
==========================================
"@ "Magenta"

# 检查PowerShell版本
function Test-PowerShellVersion {
    Write-Step "检查PowerShell版本..."
    
    $version = $PSVersionTable.PSVersion
    Write-Info "PowerShell版本: $version"
    
    if ($version.Major -lt 5) {
        Write-Error "需要PowerShell 5.0或更高版本"
        Write-Warning "请升级PowerShell: https://github.com/PowerShell/PowerShell"
        exit 1
    }
    
    Write-Success "PowerShell版本检查通过"
}

# 检查Python
function Test-Python {
    Write-Step "检查Python安装..."
    
    $pythonCmd = $null
    
    # 尝试不同的Python命令
    foreach ($cmd in @("python", "python3", "py")) {
        try {
            $version = & $cmd --version 2>&1
            if ($LASTEXITCODE -eq 0) {
                $pythonCmd = $cmd
                break
            }
        }
        catch {
            continue
        }
    }
    
    if (-not $pythonCmd) {
        Write-Error "未找到Python"
        Write-Warning "请从以下地址安装Python 3.10+:"
        Write-Warning "https://www.python.org/downloads/"
        exit 1
    }
    
    $versionString = & $pythonCmd --version 2>&1
    Write-Info "Python版本: $versionString"
    
    # 检查版本
    $versionMatch = $versionString -match "Python (\d+)\.(\d+)"
    if ($versionMatch) {
        $major = [int]$matches[1]
        $minor = [int]$matches[2]
        
        if ($major -lt 3 -or ($major -eq 3 -and $minor -lt 10)) {
            Write-Error "Python版本过低，需要3.10或更高版本"
            exit 1
        }
    }
    
    Write-Success "Python版本检查通过"
    return $pythonCmd
}

# 安装UV
function Install-UV {
    Write-Step "检查UV包管理器..."
    
    # 检查UV是否已安装
    try {
        $uvVersion = uv --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "UV已安装: $uvVersion"
            return $true
        }
    }
    catch {
        # UV未安装
    }
    
    if (-not $Force) {
        Write-Warning "UV未安装"
        $install = Read-Host "是否安装UV? (y/N)"
        if ($install -ne "y" -and $install -ne "Y") {
            Write-Warning "跳过UV安装，将使用pip"
            return $false
        }
    }
    
    Write-Step "安装UV..."
    
    try {
        # 使用官方安装脚本
        Invoke-RestMethod https://astral.sh/uv/install.ps1 | Invoke-Expression
        
        # 刷新环境变量
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        # 验证安装
        $uvVersion = uv --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "UV安装成功: $uvVersion"
            return $true
        }
        else {
            throw "UV安装验证失败"
        }
    }
    catch {
        Write-Error "UV安装失败: $_"
        Write-Warning "将使用pip作为备选方案"
        return $false
    }
}

# 设置Python版本（使用UV）
function Set-PythonVersion {
    param([bool]$UseUV)
    
    if (-not $UseUV) {
        Write-Warning "跳过Python版本管理（UV不可用）"
        return
    }
    
    Write-Step "设置项目Python版本..."
    
    try {
        Write-Info "安装Python 3.11..."
        uv python install 3.11
        
        Write-Info "固定项目Python版本..."
        uv python pin 3.11
        
        Write-Success "Python版本设置完成"
    }
    catch {
        Write-Warning "Python版本设置失败: $_"
    }
}

# 安装依赖
function Install-Dependencies {
    param(
        [bool]$UseUV,
        [string]$PythonCmd
    )
    
    Write-Step "安装项目依赖..."
    
    if ($UseUV) {
        try {
            Write-Info "使用UV同步依赖..."
            uv sync
            
            Write-Info "安装开发依赖..."
            uv sync --extra dev
            
            Write-Success "依赖安装完成（UV）"
            return $true
        }
        catch {
            Write-Warning "UV依赖安装失败: $_"
            Write-Warning "回退到pip安装..."
        }
    }
    
    # 使用pip安装
    try {
        Write-Info "创建虚拟环境..."
        & $PythonCmd -m venv venv
        
        Write-Info "激活虚拟环境..."
        & ".\venv\Scripts\Activate.ps1"
        
        Write-Info "升级pip..."
        & $PythonCmd -m pip install --upgrade pip
        
        Write-Info "安装依赖..."
        & $PythonCmd -m pip install -r requirements.txt
        
        Write-Success "依赖安装完成（pip）"
        return $false
    }
    catch {
        Write-Error "依赖安装失败: $_"
        exit 1
    }
}

# 验证安装
function Test-Installation {
    param([bool]$UseUV)
    
    Write-Step "验证安装..."
    
    if ($UseUV) {
        try {
            Write-Info "测试UV环境..."
            uv run python --version
            
            Write-Info "列出已安装的包..."
            uv pip list
        }
        catch {
            Write-Warning "UV环境测试失败: $_"
        }
    }
    else {
        try {
            Write-Info "激活虚拟环境..."
            & ".\venv\Scripts\Activate.ps1"
            
            Write-Info "测试Python环境..."
            python --version
            
            Write-Info "列出已安装的包..."
            pip list
        }
        catch {
            Write-Warning "Python环境测试失败: $_"
        }
    }
    
    Write-Success "安装验证完成"
}

# 显示使用说明
function Show-Usage {
    param([bool]$UseUV)
    
    Write-Host ""
    Write-Success "🎉 开发环境设置完成！"
    Write-Host "=================================="
    Write-Host ""
    
    Write-ColorText "📖 使用说明:" "Blue"
    
    if ($UseUV) {
        Write-ColorText "🚀 启动应用:" "Yellow"
        Write-Host "   uv run python -m modern_gui_app"
        Write-Host ""
        Write-ColorText "🔧 开发命令:" "Yellow"
        Write-Host "   uv run black .              # 代码格式化"
        Write-Host "   uv run isort .              # 导入排序"
        Write-Host "   uv run flake8 .             # 代码检查"
        Write-Host "   uv run pytest              # 运行测试"
        Write-Host ""
        Write-ColorText "📦 依赖管理:" "Yellow"
        Write-Host "   uv add package-name         # 添加依赖"
        Write-Host "   uv add --dev package-name   # 添加开发依赖"
        Write-Host "   uv remove package-name      # 移除依赖"
        Write-Host "   uv sync                     # 同步依赖"
        Write-Host "   uv lock --upgrade           # 升级依赖"
    }
    else {
        Write-ColorText "🚀 启动应用:" "Yellow"
        Write-Host "   .\venv\Scripts\Activate.ps1 # 激活虚拟环境"
        Write-Host "   python -m modern_gui_app    # 启动应用"
        Write-Host ""
        Write-ColorText "💡 建议安装UV以获得更好的开发体验:" "Yellow"
        Write-Host "   iwr https://astral.sh/uv/install.ps1 | iex"
    }
    
    Write-Host ""
    Write-ColorText "📚 更多信息:" "Blue"
    Write-Host "   README.md                   # 项目文档"
    Write-Host "   docs\                       # 详细文档"
    Write-Host ""
}

# 主函数
function Main {
    try {
        Write-Info "开始设置Windows开发环境..."
        Write-Host ""
        
        Test-PowerShellVersion
        $pythonCmd = Test-Python
        $useUV = Install-UV
        Set-PythonVersion -UseUV $useUV
        $finalUseUV = Install-Dependencies -UseUV $useUV -PythonCmd $pythonCmd
        Test-Installation -UseUV $finalUseUV
        Show-Usage -UseUV $finalUseUV
        
        Write-ColorText "🎊 所有设置完成！祝您开发愉快！" "Green"
    }
    catch {
        Write-Error "设置过程中出现错误: $_"
        Write-Warning "请检查错误信息并重试"
        exit 1
    }
}

# 运行主函数
Main
