#!/usr/bin/env python3
"""
Modern GUI App Template - Icon Generation Script

图标生成脚本，用于从源图像生成各平台所需的图标文件。
基于 Augment-Code-Free 项目的图标处理最佳实践。

主要功能：
- 从高质量源图像生成多平台图标
- 支持Windows ICO、macOS ICNS、Linux PNG格式
- 自动化图标尺寸处理
- 图标质量优化和验证

支持的输入格式：
- PNG (推荐，支持透明度)
- SVG (矢量图，最佳质量)
- JPG/JPEG (不推荐，无透明度)

输出格式：
- Windows: app.ico (多尺寸ICO文件)
- macOS: app.icns (Apple图标格式)
- Linux: app_*.png (多个PNG文件)

使用方法：
    python scripts/generate_icons.py source_icon.png
    python scripts/generate_icons.py source_icon.svg --output-dir icons/
    python scripts/generate_icons.py --help

依赖：
    pip install Pillow  # 图像处理
    # 可选：pip install cairosvg  # SVG支持
"""

import argparse
import os
import sys
import subprocess
from pathlib import Path
from typing import List, Tuple, Optional

try:
    from PIL import Image, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import cairosvg
    SVG_SUPPORT = True
except ImportError:
    SVG_SUPPORT = False

# 图标尺寸配置
ICON_SIZES = {
    "ico": [16, 32, 48, 64, 128, 256],  # Windows ICO
    "icns": [16, 32, 64, 128, 256, 512, 1024],  # macOS ICNS
    "png": [16, 32, 48, 64, 128, 256, 512],  # Linux PNG
}

# macOS ICNS文件名映射
ICNS_MAPPING = {
    16: "icon_16x16.png",
    32: "<EMAIL>",
    64: "<EMAIL>",
    128: "icon_128x128.png",
    256: "<EMAIL>",
    512: "<EMAIL>",
    1024: "<EMAIL>",
}

class IconGeneratorError(Exception):
    """图标生成错误"""
    pass

class IconGenerator:
    """图标生成器"""
    
    def __init__(self, source_path: Path, output_dir: Path, verbose: bool = False):
        self.source_path = source_path
        self.output_dir = output_dir
        self.verbose = verbose
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        if self.verbose:
            print(f"📁 源文件: {self.source_path}")
            print(f"📁 输出目录: {self.output_dir}")
    
    def _log(self, message: str):
        """输出日志"""
        if self.verbose:
            print(message)
    
    def _check_dependencies(self):
        """检查依赖"""
        if not PIL_AVAILABLE:
            raise IconGeneratorError(
                "缺少Pillow库，请安装: pip install Pillow"
            )
        
        if self.source_path.suffix.lower() == '.svg' and not SVG_SUPPORT:
            raise IconGeneratorError(
                "SVG支持需要cairosvg库，请安装: pip install cairosvg"
            )
    
    def _load_source_image(self) -> Image.Image:
        """加载源图像"""
        self._log(f"📖 加载源图像: {self.source_path}")
        
        if not self.source_path.exists():
            raise IconGeneratorError(f"源文件不存在: {self.source_path}")
        
        try:
            if self.source_path.suffix.lower() == '.svg':
                # 处理SVG文件
                if not SVG_SUPPORT:
                    raise IconGeneratorError("SVG支持未安装")
                
                # 将SVG转换为PNG
                png_data = cairosvg.svg2png(
                    url=str(self.source_path),
                    output_width=1024,
                    output_height=1024
                )
                
                from io import BytesIO
                image = Image.open(BytesIO(png_data))
            else:
                # 处理其他格式
                image = Image.open(self.source_path)
            
            # 确保是RGBA模式（支持透明度）
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            self._log(f"✅ 图像加载成功: {image.size}, 模式: {image.mode}")
            return image
            
        except Exception as e:
            raise IconGeneratorError(f"加载图像失败: {e}")
    
    def _resize_image(self, image: Image.Image, size: int, 
                     use_lanczos: bool = True) -> Image.Image:
        """调整图像大小"""
        if image.size[0] != image.size[1]:
            # 如果不是正方形，先裁剪为正方形
            min_size = min(image.size)
            left = (image.size[0] - min_size) // 2
            top = (image.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            image = image.crop((left, top, right, bottom))
        
        # 调整大小
        if use_lanczos and size < image.size[0]:
            # 缩小时使用Lanczos算法获得更好质量
            resized = image.resize((size, size), Image.Resampling.LANCZOS)
        else:
            # 放大时使用双三次插值
            resized = image.resize((size, size), Image.Resampling.BICUBIC)
        
        # 对小尺寸图标进行锐化
        if size <= 32:
            resized = resized.filter(ImageFilter.UnsharpMask(radius=0.5, percent=150))
        
        return resized
    
    def generate_ico(self, image: Image.Image) -> Path:
        """生成Windows ICO文件"""
        self._log("🖼️  生成Windows ICO文件...")
        
        ico_path = self.output_dir / "app.ico"
        
        # 生成多个尺寸的图像
        ico_images = []
        for size in ICON_SIZES["ico"]:
            resized = self._resize_image(image, size)
            ico_images.append(resized)
            self._log(f"  📏 生成 {size}x{size} 图标")
        
        # 保存ICO文件
        ico_images[0].save(
            ico_path,
            format='ICO',
            sizes=[(img.size[0], img.size[1]) for img in ico_images],
            append_images=ico_images[1:]
        )
        
        self._log(f"✅ ICO文件生成: {ico_path}")
        return ico_path
    
    def generate_icns(self, image: Image.Image) -> Path:
        """生成macOS ICNS文件"""
        self._log("🍎 生成macOS ICNS文件...")
        
        icns_path = self.output_dir / "app.icns"
        
        # 创建临时iconset目录
        iconset_dir = self.output_dir / "app.iconset"
        iconset_dir.mkdir(exist_ok=True)
        
        try:
            # 生成各种尺寸的PNG文件
            for size in ICON_SIZES["icns"]:
                resized = self._resize_image(image, size)
                
                # 确定文件名
                if size in ICNS_MAPPING:
                    filename = ICNS_MAPPING[size]
                else:
                    filename = f"icon_{size}x{size}.png"
                
                png_path = iconset_dir / filename
                resized.save(png_path, format='PNG')
                self._log(f"  📏 生成 {filename}")
            
            # 使用iconutil生成ICNS文件（仅在macOS上可用）
            if sys.platform == 'darwin':
                try:
                    subprocess.run([
                        'iconutil', '-c', 'icns', str(iconset_dir),
                        '-o', str(icns_path)
                    ], check=True, capture_output=True)
                    self._log(f"✅ ICNS文件生成: {icns_path}")
                except subprocess.CalledProcessError as e:
                    self._log(f"⚠️  iconutil失败，使用备用方法: {e}")
                    self._generate_icns_fallback(iconset_dir, icns_path)
            else:
                self._log("⚠️  非macOS系统，使用备用ICNS生成方法")
                self._generate_icns_fallback(iconset_dir, icns_path)
            
        finally:
            # 清理临时文件
            import shutil
            if iconset_dir.exists():
                shutil.rmtree(iconset_dir)
        
        return icns_path
    
    def _generate_icns_fallback(self, iconset_dir: Path, icns_path: Path):
        """备用ICNS生成方法"""
        try:
            # 尝试使用png2icns（如果可用）
            subprocess.run([
                'png2icns', str(icns_path), str(iconset_dir / "<EMAIL>")
            ], check=True, capture_output=True)
            self._log(f"✅ ICNS文件生成（png2icns）: {icns_path}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            # 如果都不可用，创建一个简单的ICNS文件
            self._log("⚠️  无法生成真正的ICNS文件，创建PNG副本")
            largest_png = iconset_dir / "<EMAIL>"
            if largest_png.exists():
                import shutil
                shutil.copy2(largest_png, icns_path.with_suffix('.png'))
                self._log(f"📄 创建PNG副本: {icns_path.with_suffix('.png')}")
    
    def generate_png_set(self, image: Image.Image) -> List[Path]:
        """生成Linux PNG文件集"""
        self._log("🐧 生成Linux PNG文件集...")
        
        png_paths = []
        
        for size in ICON_SIZES["png"]:
            resized = self._resize_image(image, size)
            png_path = self.output_dir / f"app_{size}.png"
            resized.save(png_path, format='PNG', optimize=True)
            png_paths.append(png_path)
            self._log(f"  📏 生成 app_{size}.png")
        
        self._log(f"✅ PNG文件集生成完成，共{len(png_paths)}个文件")
        return png_paths
    
    def generate_all(self) -> dict:
        """生成所有平台的图标"""
        self._log("🚀 开始生成所有平台图标...")
        
        self._check_dependencies()
        source_image = self._load_source_image()
        
        results = {}
        
        try:
            # 生成Windows ICO
            results['ico'] = self.generate_ico(source_image)
        except Exception as e:
            self._log(f"❌ ICO生成失败: {e}")
            results['ico'] = None
        
        try:
            # 生成macOS ICNS
            results['icns'] = self.generate_icns(source_image)
        except Exception as e:
            self._log(f"❌ ICNS生成失败: {e}")
            results['icns'] = None
        
        try:
            # 生成Linux PNG集
            results['png'] = self.generate_png_set(source_image)
        except Exception as e:
            self._log(f"❌ PNG集生成失败: {e}")
            results['png'] = None
        
        self._log("🎉 图标生成完成！")
        return results
    
    def validate_icons(self, results: dict):
        """验证生成的图标"""
        self._log("🔍 验证生成的图标...")
        
        for format_name, paths in results.items():
            if paths is None:
                self._log(f"⚠️  {format_name.upper()} 格式未生成")
                continue
            
            if isinstance(paths, list):
                for path in paths:
                    if path.exists():
                        size = path.stat().st_size
                        self._log(f"✅ {path.name}: {size} bytes")
                    else:
                        self._log(f"❌ {path.name}: 文件不存在")
            else:
                if paths.exists():
                    size = paths.stat().st_size
                    self._log(f"✅ {paths.name}: {size} bytes")
                else:
                    self._log(f"❌ {paths.name}: 文件不存在")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Modern GUI App 图标生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "source",
        type=Path,
        help="源图像文件路径（PNG、SVG、JPG）"
    )
    
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path.cwd(),
        help="输出目录（默认：当前目录）"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="验证生成的图标"
    )
    
    args = parser.parse_args()
    
    try:
        generator = IconGenerator(args.source, args.output_dir, args.verbose)
        results = generator.generate_all()
        
        if args.validate:
            generator.validate_icons(results)
        
        print("🎉 图标生成成功！")
        
        # 显示生成的文件
        print("\n📁 生成的文件：")
        for format_name, paths in results.items():
            if paths is None:
                continue
            
            if isinstance(paths, list):
                for path in paths:
                    if path.exists():
                        print(f"  {path}")
            else:
                if paths.exists():
                    print(f"  {paths}")
        
    except IconGeneratorError as e:
        print(f"❌ 图标生成失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
