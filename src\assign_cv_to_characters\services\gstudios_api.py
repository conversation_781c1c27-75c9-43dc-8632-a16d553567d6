"""
GStudios API 工具模块
用于处理与 gstudios.com.cn 网站的API交互
"""

import requests
from typing import Union, Callable, Dict, List, Tuple
from urllib3.exceptions import InsecureRequestWarning

# 禁用 SSL 警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 定义API的基础URL地址
BASE_URL = "https://www.gstudios.com.cn/story_v2/api"

# API端点配置字典，包含所有可用的API接口路径
API_ENDPOINTS = {
    'PARTNER_LIST': '/content/book/partner/list',           # 获取合作伙伴列表的接口
    'INVITE_PARTNER': '/content/book/relation/freelancer/invite',    # 邀请合作伙伴的接口
    'CANCEL_INVITE': '/content/book/relation/freelancer/invite/cancel',    # 取消邀请的接口
    'GET_INVITED_PARTNERS': '/content/book/relation/freelancer/by/book',   # 获取已邀请合作伙伴列表的接口
    'BOOK_LIST': '/content/book/list/editor',               # 获取编辑可见的书籍列表接口
    'SEND_INVITATION': '/content/book/partner/invite',      # 发送邀请接口
    'CHARACTER_LIST': '/content/character/list/book',       # 获取角色列表接口
    'CV_LIST_HUMAN': '/record/cv/list/human',               # 获取人工CV列表接口
    'CV_LIST_ROBOT': '/record/cv/list/robot',               # 获取AI CV列表接口
    'CHARACTER_UPDATE': '/content/character/update/info'    # 更新角色信息接口
}

class GStudiosAPI:
    """GStudios API客户端"""

    def __init__(self, base_url=None, token=None):
        """
        初始化API客户端

        Args:
            base_url: API基础URL，默认使用BASE_URL
            token: 授权Token，默认为None
        """
        # 参数校验和纠错
        # 如果只传入了一个参数，且它看起来像token（不包含http或域名），则认为是token
        if base_url and token is None and not (base_url.startswith('http') or '.' in base_url):
            token = base_url
            base_url = None
            print(f"检测到参数顺序错误，已自动纠正：token={token}")
        
        # 确保base_url有值
        self.base_url = base_url or BASE_URL
        
        # 确保base_url格式正确
        if not self.base_url.startswith('http'):
            self.base_url = f"https://{self.base_url}"
        
        # 移除末尾的斜杠，确保与API_ENDPOINTS拼接时不会出现双斜杠
        if self.base_url.endswith('/'):
            self.base_url = self.base_url[:-1]
        
        self.token = token
        self.headers = {
            "x-requested-with": "XMLHttpRequest",
            "accept": "application/json, text/plain, */*"
        }

        if token:
            self.set_token(token)

    def set_token(self, token):
        """设置授权Token"""
        self.token = token
        self.headers["authorization"] = f"Bearer {token}"

    def get_book_list(self, finished: str = "all") -> Tuple[bool, List[Dict]]:
        """
        获取书籍列表

        Args:
            finished: 书籍完成状态
                - "all": 获取所有书籍
                - "finished": 获取已完成的书籍
                - "unfinished": 获取未完成的书籍

        Returns:
            Tuple[bool, List[Dict]]: (成功标志, 书籍列表)
        """
        url = f"{self.base_url}{API_ENDPOINTS['BOOK_LIST']}"

        # 将字符串状态转换为API期望的布尔值参数
        params = {}
        if finished == "finished":
            params["finished"] = True
        elif finished == "unfinished":
            params["finished"] = False
        # 如果是"all"或其他值，不传finished参数，获取所有书籍

        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                error_msg = data.get('msg', '未知错误')
                error_code = data.get('code', '未知')
                print(f"获取书籍列表失败: code={error_code}, msg={error_msg}")
                print(f"请求URL: {url}")
                print(f"请求参数: {params}")
                print(f"响应数据: {data}")
                return False, []

            return True, data.get('data', {}).get('list', [])

        except requests.exceptions.RequestException as e:
            print(f"获取书籍列表时网络错误: {e}")
            return False, []
        except Exception as e:
            print(f"获取书籍列表时发生未知错误: {e}")
            return False, []

    def get_character_list(self, book_id: str) -> Tuple[bool, List[Dict]]:
        """
        获取角色列表

        Args:
            book_id: 书籍ID

        Returns:
            Tuple[bool, List[Dict]]: (成功标志, 角色列表)
        """
        url = f"{self.base_url}{API_ENDPOINTS['CHARACTER_LIST']}"
        params = {"bookId": book_id}

        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                error_msg = data.get('msg', '未知错误')
                print(f"获取角色列表失败: code={data.get('code')}, msg={error_msg}")
                return False, []

            # 处理不同的数据结构
            data_content = data.get('data', [])
            if isinstance(data_content, dict):
                # 如果data是字典，尝试获取list字段
                return True, data_content.get('list', [])
            elif isinstance(data_content, list):
                # 如果data直接是列表，直接返回
                return True, data_content
            else:
                # 其他情况返回空列表
                return True, []

        except requests.exceptions.RequestException as e:
            print(f"获取角色列表时网络错误: {e}")
            return False, []
        except Exception as e:
            print(f"获取角色列表时发生未知错误: {e}")
            return False, []

    def get_cv_list(self, book_id: str) -> Tuple[bool, List[Dict]]:
        """
        获取CV列表

        Args:
            book_id: 书籍ID

        Returns:
            Tuple[bool, List[Dict]]: (成功标志, CV列表)
        """
        url = f"{self.base_url}{API_ENDPOINTS['CV_LIST_HUMAN']}"
        params = {"bookId": book_id}

        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                error_msg = data.get('msg', '未知错误')
                print(f"获取CV列表失败: code={data.get('code')}, msg={error_msg}")
                return False, []

            return True, data.get('data', {}).get('list', [])

        except requests.exceptions.RequestException as e:
            print(f"获取CV列表时网络错误: {e}")
            return False, []
        except Exception as e:
            print(f"获取CV列表时发生未知错误: {e}")
            return False, []

    def assign_cv_to_character(self, character_id: str, cv_id: str) -> Tuple[bool, str]:
        """
        分配CV给角色

        Args:
            character_id: 角色ID
            cv_id: CV ID

        Returns:
            Tuple[bool, str]: (成功标志, 成功/错误消息)
        """
        url = f"{self.base_url}{API_ENDPOINTS['CHARACTER_UPDATE']}"
        data = {
            "id": character_id,
            "cvHumanId": cv_id
        }

        try:
            response = requests.post(url, headers=self.headers, json=data, verify=False)
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 1:
                return True, "分配成功"
            else:
                error_msg = result.get('msg', '未知错误')
                return False, f"分配失败: {error_msg}"

        except requests.exceptions.RequestException as e:
            return False, f"网络错误: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"
