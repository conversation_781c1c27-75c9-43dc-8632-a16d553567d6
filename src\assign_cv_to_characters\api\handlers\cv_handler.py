"""
CV处理器

此模块提供CV简名管理和匹配功能。
"""
import json
import os
from typing import Dict, Any
from ...services.cv_service import CVService
from ...services.config_service import ConfigService
from ...utils.helpers import format_success_response, format_error_response


def get_cv_nicknames(config_service: ConfigService) -> Dict[str, Any]:
    """获取CV简名映射

    Args:
        config_service: 配置服务实例

    Returns:
        Dict[str, Any]: CV简名映射数据
    """
    try:
        files_config = config_service.get_files_config()
        cv_config_file = files_config['cv_config_file']

        cv_nicknames = {}

        if os.path.exists(cv_config_file):
            try:
                with open(cv_config_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)

                    # 处理两种可能的格式
                    if "CV简名对照表" in loaded_data and isinstance(loaded_data["CV简名对照表"], list):
                        # 嵌套格式: {"CV简名对照表": [{"简名": "x", "全名": "y"}, ...]}
                        for item in loaded_data["CV简名对照表"]:
                            if "简名" in item and "全名" in item:
                                cv_nicknames[item["简名"]] = item["全名"]
                    else:
                        # 简单格式: {"简名1": "全名1", "简名2": "全名2", ...}
                        cv_nicknames = loaded_data
            except (json.JSONDecodeError, FileNotFoundError):
                cv_nicknames = {}

        return format_success_response(
            data={
                "cv_nicknames": cv_nicknames,
                "count": len(cv_nicknames),
                "config_file": cv_config_file
            },
            message=f"成功获取{len(cv_nicknames)}个CV简名映射"
        )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取CV简名映射失败"
        )


def save_cv_nicknames(cv_nicknames: Dict[str, str], config_service: ConfigService) -> Dict[str, Any]:
    """保存CV简名映射
    
    Args:
        cv_nicknames: CV简名到全名的映射字典
        config_service: 配置服务实例
        
    Returns:
        Dict[str, Any]: 保存结果
    """
    try:
        if not isinstance(cv_nicknames, dict):
            return format_error_response(
                error="CV简名数据必须是字典格式",
                message="数据格式错误"
            )
        
        files_config = config_service.get_files_config()
        cv_config_file = files_config['cv_config_file']
        
        # 确保目录存在
        config_dir = os.path.dirname(cv_config_file)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        
        # 保存到文件
        with open(cv_config_file, 'w', encoding='utf-8') as f:
            json.dump(cv_nicknames, f, ensure_ascii=False, indent=2)
        
        return format_success_response(
            data={
                "cv_nicknames": cv_nicknames,
                "count": len(cv_nicknames),
                "config_file": cv_config_file
            },
            message=f"成功保存{len(cv_nicknames)}个CV简名映射"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="保存CV简名映射失败"
        )


def match_cvs(character_cv_mapping: Dict[str, str], characters: list, cvs: list, 
              cv_service: CVService) -> Dict[str, Any]:
    """匹配角色和CV
    
    Args:
        character_cv_mapping: 角色到CV的映射
        characters: 角色列表
        cvs: CV列表
        cv_service: CV服务实例
        
    Returns:
        Dict[str, Any]: 匹配结果
    """
    try:
        # 转换为CV服务需要的格式
        from ...models.character import Character
        from ...models.cv import CV
        
        character_objects = []
        cv_objects = []
        
        # 转换角色数据
        for char_data in characters:
            if isinstance(char_data, dict):
                character_objects.append(Character(
                    id=char_data.get('id', ''),
                    name=char_data.get('name', ''),
                    cv_human_id=char_data.get('cv_human_id')
                ))
        
        # 转换CV数据
        for cv_data in cvs:
            if isinstance(cv_data, dict):
                cv_objects.append(CV(
                    id=cv_data.get('id', ''),
                    name=cv_data.get('name', '')
                ))
        
        # 执行匹配
        match_results = cv_service.batch_match_cvs(
            character_cv_mapping, 
            character_objects, 
            cv_objects
        )
        
        return format_success_response(
            data=match_results,
            message=f"匹配完成：成功{len(match_results['success_matches'])}个，失败{len(match_results['unmatched_characters']) + len(match_results['unmatched_cvs'])}个"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="CV匹配失败"
        )


def update_cv_nickname_mapping(nickname: str, fullname: str, config_service: ConfigService) -> Dict[str, Any]:
    """更新单个CV简名映射
    
    Args:
        nickname: CV简名
        fullname: CV全名
        config_service: 配置服务实例
        
    Returns:
        Dict[str, Any]: 更新结果
    """
    try:
        if not nickname or not fullname:
            return format_error_response(
                error="简名和全名都不能为空",
                message="请提供有效的简名和全名"
            )
        
        # 获取现有映射
        current_result = get_cv_nicknames(config_service)
        if not current_result["success"]:
            return current_result
        
        cv_nicknames = current_result["data"]["cv_nicknames"]
        cv_nicknames[nickname.strip()] = fullname.strip()
        
        # 保存更新后的映射
        return save_cv_nicknames(cv_nicknames, config_service)
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="更新CV简名映射失败"
        )


def delete_cv_nickname_mapping(nickname: str, config_service: ConfigService) -> Dict[str, Any]:
    """删除CV简名映射
    
    Args:
        nickname: 要删除的CV简名
        config_service: 配置服务实例
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        if not nickname:
            return format_error_response(
                error="简名不能为空",
                message="请提供要删除的简名"
            )
        
        # 获取现有映射
        current_result = get_cv_nicknames(config_service)
        if not current_result["success"]:
            return current_result
        
        cv_nicknames = current_result["data"]["cv_nicknames"]
        
        if nickname not in cv_nicknames:
            return format_error_response(
                error="简名不存在",
                message=f"简名 '{nickname}' 不存在"
            )
        
        del cv_nicknames[nickname]
        
        # 保存更新后的映射
        result = save_cv_nicknames(cv_nicknames, config_service)
        if result["success"]:
            result["message"] = f"成功删除简名 '{nickname}'"
        
        return result
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="删除CV简名映射失败"
        )
