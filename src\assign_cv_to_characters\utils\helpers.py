"""
Modern GUI App Template - Helper Utilities

通用辅助函数模块。
提供各种常用的工具函数，包括响应格式化、参数验证、JSON处理等。

主要功能：
- 统一的API响应格式化
- 参数验证和类型检查
- 安全的JSON操作
- 字符串和数据处理工具
- 时间和日期工具

设计原则：
- 纯函数设计，无副作用
- 完善的错误处理
- 类型提示支持
- 易于测试和维护
"""

import json
import time
import hashlib
import re
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union, Callable


def format_success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
    """
    格式化成功响应。
    
    Args:
        data (Any, optional): 响应数据
        message (str): 成功消息
    
    Returns:
        Dict[str, Any]: 格式化的成功响应
    
    Examples:
        >>> response = format_success_response({"count": 5}, "数据获取成功")
        >>> print(response)
        {'success': True, 'data': {'count': 5}, 'message': '数据获取成功', 'timestamp': '...'}
    """
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": get_current_timestamp(),
    }


def format_error_response(error: str, message: str = "操作失败", traceback: str = None) -> Dict[str, Any]:
    """
    格式化错误响应。
    
    Args:
        error (str): 错误信息
        message (str): 错误消息
        traceback (str, optional): 错误堆栈信息
    
    Returns:
        Dict[str, Any]: 格式化的错误响应
    
    Examples:
        >>> response = format_error_response("文件不存在", "读取文件失败")
        >>> print(response)
        {'success': False, 'error': '文件不存在', 'message': '读取文件失败', 'timestamp': '...'}
    """
    response = {
        "success": False,
        "error": error,
        "message": message,
        "timestamp": get_current_timestamp(),
    }
    
    if traceback:
        response["traceback"] = traceback
    
    return response


def validate_params(params: Dict[str, Any], required_params: List[str]) -> Dict[str, Any]:
    """
    验证参数是否包含所有必需的字段。
    
    Args:
        params (Dict[str, Any]): 要验证的参数字典
        required_params (List[str]): 必需参数列表
    
    Returns:
        Dict[str, Any]: 验证结果
    
    Examples:
        >>> result = validate_params({"name": "test"}, ["name", "age"])
        >>> print(result)
        {'valid': False, 'missing': ['age'], 'error': '缺少必需参数: age'}
    """
    if not isinstance(params, dict):
        return {
            "valid": False,
            "error": "参数必须是字典格式",
            "missing": required_params,
        }
    
    missing_params = [param for param in required_params if param not in params]
    
    if missing_params:
        return {
            "valid": False,
            "missing": missing_params,
            "error": f"缺少必需参数: {', '.join(missing_params)}",
        }
    
    return {
        "valid": True,
        "missing": [],
        "error": None,
    }


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全地解析JSON字符串。
    
    Args:
        json_str (str): JSON字符串
        default (Any): 解析失败时的默认值
    
    Returns:
        Any: 解析结果或默认值
    
    Examples:
        >>> data = safe_json_loads('{"name": "test"}', {})
        >>> print(data)  # {'name': 'test'}
        >>> data = safe_json_loads('invalid json', {})
        >>> print(data)  # {}
    """
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError, ValueError):
        return default


def safe_json_dumps(data: Any, default: str = "{}") -> str:
    """
    安全地序列化为JSON字符串。
    
    Args:
        data (Any): 要序列化的数据
        default (str): 序列化失败时的默认值
    
    Returns:
        str: JSON字符串或默认值
    
    Examples:
        >>> json_str = safe_json_dumps({"name": "test"})
        >>> print(json_str)  # '{"name": "test"}'
    """
    try:
        return json.dumps(data, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        return default


def get_current_timestamp() -> str:
    """
    获取当前时间戳（ISO格式）。
    
    Returns:
        str: ISO格式的时间戳
    
    Examples:
        >>> timestamp = get_current_timestamp()
        >>> print(timestamp)  # '2024-01-01T12:00:00+00:00'
    """
    return datetime.now(timezone.utc).isoformat()


def get_unix_timestamp() -> int:
    """
    获取当前Unix时间戳。
    
    Returns:
        int: Unix时间戳
    
    Examples:
        >>> timestamp = get_unix_timestamp()
        >>> print(timestamp)  # 1704110400
    """
    return int(time.time())


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小为人类可读的格式。
    
    Args:
        size_bytes (int): 文件大小（字节）
    
    Returns:
        str: 格式化的文件大小
    
    Examples:
        >>> size = format_file_size(1024)
        >>> print(size)  # '1.0 KB'
        >>> size = format_file_size(1048576)
        >>> print(size)  # '1.0 MB'
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def generate_hash(data: str, algorithm: str = "md5") -> str:
    """
    生成数据的哈希值。
    
    Args:
        data (str): 要哈希的数据
        algorithm (str): 哈希算法 (md5, sha1, sha256)
    
    Returns:
        str: 哈希值
    
    Examples:
        >>> hash_value = generate_hash("hello world")
        >>> print(hash_value)  # '5d41402abc4b2a76b9719d911017c592'
    """
    try:
        if algorithm == "md5":
            return hashlib.md5(data.encode()).hexdigest()
        elif algorithm == "sha1":
            return hashlib.sha1(data.encode()).hexdigest()
        elif algorithm == "sha256":
            return hashlib.sha256(data.encode()).hexdigest()
        else:
            return hashlib.md5(data.encode()).hexdigest()
    except Exception:
        return ""


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全的字符。
    
    Args:
        filename (str): 原始文件名
    
    Returns:
        str: 清理后的文件名
    
    Examples:
        >>> clean_name = sanitize_filename("file<>name?.txt")
        >>> print(clean_name)  # 'filename.txt'
    """
    # 移除或替换不安全的字符
    unsafe_chars = r'[<>:"/\\|?*]'
    clean_name = re.sub(unsafe_chars, '', filename)
    
    # 移除控制字符
    clean_name = ''.join(char for char in clean_name if ord(char) >= 32)
    
    # 限制长度
    if len(clean_name) > 255:
        name, ext = clean_name.rsplit('.', 1) if '.' in clean_name else (clean_name, '')
        max_name_len = 255 - len(ext) - 1 if ext else 255
        clean_name = name[:max_name_len] + ('.' + ext if ext else '')
    
    return clean_name or "unnamed"


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断字符串到指定长度。
    
    Args:
        text (str): 原始字符串
        max_length (int): 最大长度
        suffix (str): 截断后缀
    
    Returns:
        str: 截断后的字符串
    
    Examples:
        >>> short_text = truncate_string("这是一个很长的文本", 10)
        >>> print(short_text)  # '这是一个很长的...'
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典。
    
    Args:
        dict1 (Dict[str, Any]): 第一个字典
        dict2 (Dict[str, Any]): 第二个字典
    
    Returns:
        Dict[str, Any]: 合并后的字典
    
    Examples:
        >>> merged = deep_merge_dicts({"a": {"b": 1}}, {"a": {"c": 2}})
        >>> print(merged)  # {'a': {'b': 1, 'c': 2}}
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result


def retry_on_failure(func: Callable, max_retries: int = 3, delay: float = 1.0) -> Any:
    """
    在失败时重试函数执行。
    
    Args:
        func (Callable): 要执行的函数
        max_retries (int): 最大重试次数
        delay (float): 重试间隔（秒）
    
    Returns:
        Any: 函数执行结果
    
    Raises:
        Exception: 重试次数用尽后的最后一个异常
    
    Examples:
        >>> def unreliable_func():
        ...     import random
        ...     if random.random() < 0.5:
        ...         raise Exception("随机失败")
        ...     return "成功"
        >>> result = retry_on_failure(unreliable_func, max_retries=3)
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as e:
            last_exception = e
            if attempt < max_retries:
                time.sleep(delay)
            else:
                raise last_exception


def is_valid_email(email: str) -> bool:
    """
    验证邮箱地址格式。
    
    Args:
        email (str): 邮箱地址
    
    Returns:
        bool: 是否为有效邮箱
    
    Examples:
        >>> valid = is_valid_email("<EMAIL>")
        >>> print(valid)  # True
        >>> valid = is_valid_email("invalid-email")
        >>> print(valid)  # False
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def is_valid_url(url: str) -> bool:
    """
    验证URL格式。
    
    Args:
        url (str): URL地址
    
    Returns:
        bool: 是否为有效URL
    
    Examples:
        >>> valid = is_valid_url("https://example.com")
        >>> print(valid)  # True
        >>> valid = is_valid_url("not-a-url")
        >>> print(valid)  # False
    """
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return re.match(pattern, url) is not None


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分割成指定大小的块。
    
    Args:
        lst (List[Any]): 要分割的列表
        chunk_size (int): 块大小
    
    Returns:
        List[List[Any]]: 分割后的列表块
    
    Examples:
        >>> chunks = chunk_list([1, 2, 3, 4, 5], 2)
        >>> print(chunks)  # [[1, 2], [3, 4], [5]]
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    扁平化嵌套字典。
    
    Args:
        d (Dict[str, Any]): 嵌套字典
        parent_key (str): 父键名
        sep (str): 键分隔符
    
    Returns:
        Dict[str, Any]: 扁平化后的字典
    
    Examples:
        >>> flat = flatten_dict({"a": {"b": {"c": 1}}})
        >>> print(flat)  # {'a.b.c': 1}
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)
