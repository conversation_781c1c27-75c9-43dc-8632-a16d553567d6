# ============================================================================
# 角色CV分配工具项目 .gitignore 文件
# ============================================================================

# ============================================================================
# Python 相关
# ============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# C 扩展
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  通常这些文件是由 python 脚本从模板写入的
#  在 PyInstaller 构建 exe 之前，因此也会生成这些文件
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 翻译
*.mo
*.pot

# Django 相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask 相关
instance/
.webassets-cache

# Scrapy 相关
.scrapy

# Sphinx 文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   对于使用 pyenv 的库，建议在此处包含 .python-version
.python-version

# pipenv
#   根据官方建议，Pipfile.lock 通常应该包含在版本控制中
#   但是，如果您有多个操作系统，建议不要提交
#   Pipfile.lock 到版本控制，而是使用 Pipfile
Pipfile.lock

# poetry
#   类似于 Pipfile.lock，通常建议包含 poetry.lock 在版本控制中
#   这在这里被注释掉，因为用户可能有不同的偏好
# poetry.lock

# pdm
#   类似于 Pipfile.lock，通常建议包含 pdm.lock 在版本控制中
#   pdm 存储项目范围的配置在 .pdm.toml 中，但建议不要包含它
#   在版本控制中，因为它是用于执行环境的
.pdm.toml

# PEP 582
__pypackages__/

# Celery 相关
celerybeat-schedule
celerybeat.pid

# SageMath 解析文件
*.sage.py

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder 项目设置
.spyderproject
.spyproject

# Rope 项目设置
.ropeproject

# mkdocs 文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre 类型检查器
.pyre/

# pytype 静态类型分析器
.pytype/

# Cython 调试符号
cython_debug/

# ============================================================================
# 开发环境相关
# ============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# 操作系统相关
# ============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 项目特定文件
# ============================================================================

# 配置文件（包含敏感信息）
config.json
config/config.json

# 用户上传的文件
uploads/
temp/
tmp/
*.xlsx
*.xls
*.csv

# 应用生成的文件
logs/
*.log
*.log.*
log_*.txt

# 缓存和临时文件
cache/
.cache/
*.tmp
*.temp
*.bak
*.backup

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 应用运行时文件
*.pid
*.lock

# 用户数据目录
user_data/
data/

# 测试输出
test_output/
test_results/

# ============================================================================
# 依赖管理
# ============================================================================

# UV 包管理器
uv.lock
.uv_cache/

# pip
pip-log.txt
pip-delete-this-directory.txt

# ============================================================================
# 构建和部署
# ============================================================================

# 构建输出
build/
dist/
*.exe
*.app
*.dmg
*.pkg

# 安装包
*.deb
*.rpm
*.tar.gz
*.zip

# ============================================================================
# 其他
# ============================================================================

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 密钥文件
*.key
*.pem
*.crt
*.p12

# 备份文件
*.orig
*.rej

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 媒体文件（如果不需要版本控制）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.mp4
# *.avi

# 文档生成
docs/_build/
docs/build/

# 性能分析
*.prof

# ============================================================================
# 保留的重要文件（确保不被排除）
# ============================================================================

# 确保这些重要文件不被意外排除
!requirements.txt
!pyproject.toml
!setup.py
!setup.cfg
!README.md
!LICENSE
!.gitignore
!.github/

# 保留示例配置文件
!config/config.example.json
!config.example.json

# 保留静态资源
!src/assign_cv_to_characters/web/
!*.html
!*.css
!*.js
!*.ico
!*.icns
