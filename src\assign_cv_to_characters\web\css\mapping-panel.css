/*
 * Mapping Panel - 映射表格面板样式
 * 包含角色-CV映射表页面的所有样式：映射头部、操作按钮、表格、分页等
 * 提供映射表格管理功能的界面样式
 */

/* ==================== 映射页面布局 ==================== */
.mapping-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.mapping-header h3 {
    margin: 0;
    flex: 1;
    min-width: 200px;
}

.mapping-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}

.mapping-actions .btn {
    white-space: nowrap;
    transition: all var(--transition-smooth);
}

.action-buttons-group {
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all var(--transition-smooth);
}

.action-buttons-group.hidden {
    display: none;
}

/* 文件信息显示区域 */
.file-info-section {
    background: var(--bg-glass-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.file-info-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.file-info-label {
    font-weight: 600;
    color: var(--text-primary);
}

.file-name {
    font-weight: 500;
    color: var(--primary-color);
}

.file-path {
    color: var(--text-secondary);
    font-size: 0.9em;
    flex: 1;
    min-width: 200px;
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
    margin-left: auto;
}

/* ==================== 角色-CV映射表页面样式 ==================== */
.mapping-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.mapping-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.mapping-table-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 固定表头的映射表格样式 */
.mapping-table-wrapper .mapping-table {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0;
    border-radius: 0;
    border: none;
}

.mapping-table-wrapper .mapping-table thead {
    flex-shrink: 0;
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.mapping-table-wrapper .mapping-table thead th {
    padding: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
}

.mapping-table-wrapper .mapping-table tbody {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar {
    width: 8px;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
    background: transparent;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.mapping-table-wrapper .mapping-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.mapping-table-wrapper .mapping-table tbody td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    vertical-align: middle;
}

/* 固定分页控件样式 */
.mapping-pagination-fixed {
    flex-shrink: 0;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    position: relative;
    z-index: 5;
    width: 100%;
    box-sizing: border-box;
}

/* 空状态样式 */
.mapping-table-wrapper p {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    margin: 0;
    font-style: italic;
}

/* 映射表格样式增强（保持向后兼容） */
.mapping-table tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.mapping-table tr:hover {
    background: var(--bg-glass-hover);
}

.mapping-table tr:hover td {
    background: transparent;
}
