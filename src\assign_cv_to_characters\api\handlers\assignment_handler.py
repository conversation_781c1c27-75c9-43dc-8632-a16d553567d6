"""
分配处理器

此模块提供角色CV分配任务的处理功能。
"""
import threading
import time
import uuid
from typing import Dict, Any, Optional
from ...services.api_service import APIService
from ...services.cv_service import CVService
from ...models.character import Character
from ...models.cv import CV
from ...utils.helpers import format_success_response, format_error_response

# 全局任务状态存储
_assignment_tasks = {}


class AssignmentTask:
    """分配任务类"""
    
    def __init__(self, task_id: str, book_id: str, character_cv_mapping: Dict[str, str], 
                 enable_real_assignment: bool, api_service: APIService, cv_service: CVService):
        self.task_id = task_id
        self.book_id = book_id
        self.character_cv_mapping = character_cv_mapping
        self.enable_real_assignment = enable_real_assignment
        self.api_service = api_service
        self.cv_service = cv_service
        
        # 任务状态
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0
        self.current_step = ""
        self.results = {}
        self.error_message = ""
        self.start_time = None
        self.end_time = None
        
        # 日志
        self.logs = []
    
    def add_log(self, message: str):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
        print(log_entry)  # 同时输出到控制台
    
    def update_progress(self, progress: int, step: str = ""):
        """更新进度"""
        self.progress = progress
        if step:
            self.current_step = step
            self.add_log(step)
    
    def run(self):
        """执行分配任务"""
        try:
            self.status = "running"
            self.start_time = time.time()
            self.add_log("开始执行分配任务")
            
            # 步骤1: 获取角色列表
            self.update_progress(10, "正在获取角色列表...")
            success, characters = self.api_service.get_character_list(self.book_id)
            if not success:
                raise Exception("获取角色列表失败")
            
            # 步骤2: 获取CV列表
            self.update_progress(20, "正在获取CV列表...")
            success, cvs = self.api_service.get_cv_list(self.book_id)
            if not success:
                raise Exception("获取CV列表失败")
            
            # 步骤3: 匹配角色和CV
            self.update_progress(30, "正在匹配角色和CV...")
            match_results = self.cv_service.batch_match_cvs(
                self.character_cv_mapping, characters, cvs
            )
            
            success_matches = match_results['success_matches']
            self.add_log(f"匹配完成：找到{len(success_matches)}个有效匹配")
            
            # 步骤4: 执行分配
            self.update_progress(40, "开始执行CV分配...")
            
            results = {
                'success': 0,
                'failed': 0,
                'skipped': 0,
                'already_assigned': 0,
                'unassigned': 0,
                'unmatched_characters': match_results['unmatched_characters'],
                'unmatched_cvs': match_results['unmatched_cvs'],
                'failed_assignments': []
            }
            
            total_assignments = len(success_matches)
            
            for i, match in enumerate(success_matches):
                character_id = match['character_id']
                cv_id = match['cv_id']
                character_name = match['character_name']
                cv_name = match['cv_name']
                
                # 更新进度
                progress = 40 + int((i / total_assignments) * 50)
                self.update_progress(progress, f"正在分配 {character_name} -> {cv_name}")
                
                if self.enable_real_assignment:
                    # 执行真实的API调用
                    success, message = self.api_service.assign_cv_to_character(character_id, cv_id)
                    
                    if success:
                        results['success'] += 1
                        self.add_log(f"✅ 成功分配: {character_name} -> {cv_name}")
                    else:
                        results['failed'] += 1
                        results['failed_assignments'].append(f"{character_name}: {message}")
                        self.add_log(f"❌ 分配失败: {character_name} -> {cv_name} ({message})")
                else:
                    # 测试模式，模拟分配
                    results['success'] += 1
                    self.add_log(f"🧪 模拟分配: {character_name} -> {cv_name}")
                
                # 添加小延迟避免API限制
                time.sleep(0.1)
            
            # 统计未匹配的数据
            results['unassigned'] = len(match_results['unmatched_characters'])
            
            self.update_progress(100, "分配任务完成")
            self.results = results
            self.status = "completed"
            self.end_time = time.time()
            
            mode_str = "测试模式" if not self.enable_real_assignment else "正式模式"
            self.add_log(f"🎉 任务完成 ({mode_str})")
            self.add_log(f"成功: {results['success']}, 失败: {results['failed']}, 未匹配: {results['unassigned']}")
            
        except Exception as e:
            self.status = "failed"
            self.error_message = str(e)
            self.end_time = time.time()
            self.add_log(f"❌ 任务失败: {str(e)}")


def start_assignment(book_id: str, character_cv_mapping: Dict[str, str], 
                    enable_real_assignment: bool, api_service: APIService, 
                    cv_service: CVService) -> Dict[str, Any]:
    """开始分配任务
    
    Args:
        book_id: 书籍ID
        character_cv_mapping: 角色到CV的映射
        enable_real_assignment: 是否启用真实分配
        api_service: API服务实例
        cv_service: CV服务实例
        
    Returns:
        Dict[str, Any]: 任务启动结果
    """
    try:
        if not book_id:
            return format_error_response(
                error="书籍ID不能为空",
                message="请先选择书籍"
            )
        
        if not character_cv_mapping:
            return format_error_response(
                error="角色-CV映射不能为空",
                message="请先加载Excel文件"
            )
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务
        task = AssignmentTask(
            task_id=task_id,
            book_id=book_id,
            character_cv_mapping=character_cv_mapping,
            enable_real_assignment=enable_real_assignment,
            api_service=api_service,
            cv_service=cv_service
        )
        
        # 存储任务
        _assignment_tasks[task_id] = task
        
        # 在后台线程中执行任务
        thread = threading.Thread(target=task.run, daemon=True)
        thread.start()
        
        mode_str = "正式模式" if enable_real_assignment else "测试模式"
        
        return format_success_response(
            data={
                "task_id": task_id,
                "status": task.status,
                "mode": mode_str,
                "character_count": len(character_cv_mapping)
            },
            message=f"分配任务已启动 ({mode_str})"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="启动分配任务失败"
        )


def get_assignment_progress(task_id: str) -> Dict[str, Any]:
    """获取分配任务进度
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict[str, Any]: 任务进度信息
    """
    try:
        if task_id not in _assignment_tasks:
            return format_error_response(
                error="任务不存在",
                message="找不到指定的分配任务"
            )
        
        task = _assignment_tasks[task_id]
        
        data = {
            "task_id": task_id,
            "status": task.status,
            "progress": task.progress,
            "current_step": task.current_step,
            "logs": task.logs[-10:],  # 只返回最近10条日志
            "start_time": task.start_time,
            "end_time": task.end_time
        }
        
        if task.status == "failed":
            data["error_message"] = task.error_message
        
        if task.status == "completed":
            data["results"] = task.results
        
        return format_success_response(
            data=data,
            message="获取任务进度成功"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取任务进度失败"
        )


def get_assignment_result(task_id: str) -> Dict[str, Any]:
    """获取分配任务结果
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict[str, Any]: 任务结果
    """
    try:
        if task_id not in _assignment_tasks:
            return format_error_response(
                error="任务不存在",
                message="找不到指定的分配任务"
            )
        
        task = _assignment_tasks[task_id]
        
        if task.status != "completed":
            return format_error_response(
                error="任务未完成",
                message=f"任务状态: {task.status}"
            )
        
        return format_success_response(
            data={
                "task_id": task_id,
                "results": task.results,
                "logs": task.logs,
                "duration": task.end_time - task.start_time if task.end_time and task.start_time else 0
            },
            message="获取任务结果成功"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取任务结果失败"
        )


def cleanup_old_tasks():
    """清理旧任务（可以定期调用）"""
    current_time = time.time()
    tasks_to_remove = []
    
    for task_id, task in _assignment_tasks.items():
        # 清理1小时前的已完成或失败任务
        if (task.status in ["completed", "failed"] and 
            task.end_time and 
            current_time - task.end_time > 3600):
            tasks_to_remove.append(task_id)
    
    for task_id in tasks_to_remove:
        del _assignment_tasks[task_id]
