# UV Lock File Template
# 这是一个UV锁定文件的模板，实际的uv.lock文件会在运行 'uv sync' 时自动生成

# UV锁定文件的作用：
# 1. 确保所有开发者使用相同版本的依赖
# 2. 提供可重现的构建环境
# 3. 记录完整的依赖关系图
# 4. 支持快速安装和缓存

# 生成锁定文件的命令：
# uv sync                    # 生成/更新锁定文件并安装依赖
# uv lock                    # 仅生成/更新锁定文件
# uv lock --upgrade          # 升级所有依赖到最新版本
# uv lock --upgrade-package package-name  # 升级特定包

# 锁定文件的基本结构示例：
# version = 1
# revision = 1
# requires-python = ">=3.10"
# 
# [[package]]
# name = "modern-gui-app"
# version = "0.1.0"
# source = { editable = "." }
# dependencies = [
#     { name = "jinja2" },
#     { name = "pywebview" },
# ]
# 
# [[package]]
# name = "jinja2"
# version = "3.1.2"
# source = { registry = "https://pypi.org/simple/" }
# dependencies = [
#     { name = "markupsafe" },
# ]
# wheels = [
#     { url = "https://files.pythonhosted.org/packages/...", hash = "sha256:..." },
# ]
# 
# [[package]]
# name = "markupsafe"
# version = "2.1.3"
# source = { registry = "https://pypi.org/simple/" }
# wheels = [
#     { url = "https://files.pythonhosted.org/packages/...", hash = "sha256:..." },
# ]
# 
# [[package]]
# name = "pywebview"
# version = "4.4.1"
# source = { registry = "https://pypi.org/simple/" }
# dependencies = [
#     { name = "typing-extensions", marker = "python_version < '3.8'" },
# ]
# wheels = [
#     { url = "https://files.pythonhosted.org/packages/...", hash = "sha256:..." },
# ]

# 使用说明：
# 1. 删除此模板文件
# 2. 运行 'uv sync' 生成真实的 uv.lock 文件
# 3. 将生成的 uv.lock 文件提交到版本控制系统
# 4. 团队成员使用 'uv sync' 安装相同版本的依赖

# 最佳实践：
# - 始终将 uv.lock 文件提交到版本控制
# - 使用 'uv sync' 而不是 'uv install' 来安装依赖
# - 定期运行 'uv lock --upgrade' 更新依赖
# - 在CI/CD中使用 'uv sync --frozen' 确保使用锁定版本

# 故障排除：
# 如果遇到依赖冲突：
# 1. 检查 pyproject.toml 中的版本约束
# 2. 运行 'uv lock --resolution=highest' 尝试解决
# 3. 手动调整有冲突的包版本
# 4. 考虑使用 'uv add package-name --optional-group dev' 分离开发依赖

# 性能优化：
# - UV会自动使用全局缓存避免重复下载
# - 支持并行下载和安装
# - 使用硬链接减少磁盘占用
# - 智能的增量更新机制
