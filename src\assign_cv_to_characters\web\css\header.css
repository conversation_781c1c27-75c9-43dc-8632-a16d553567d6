/*
 * Header - 头部样式
 * 包含应用头部的所有样式：头部容器、标题、书籍选择、状态指示器等
 * 提供固定头部布局和交互效果
 */

/* ==================== Header ==================== */
.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 60px; /* 从80px减少到60px */
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-sm) var(--spacing-md); /* 从1rem 1.5rem减少到0.5rem 1rem */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    box-sizing: border-box;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 头部折叠按钮样式 */
.sidebar-toggle-header {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.sidebar-toggle-header:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.sidebar-toggle-header:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* 头部折叠按钮的波纹效果 */
.sidebar-toggle-header::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.sidebar-toggle-header:active::before {
    width: 40px;
    height: 40px;
}

.sidebar-toggle-header .toggle-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-smooth);
    font-weight: bold;
    display: inline-block;
    line-height: 1;
}

.sidebar.collapsed .sidebar-toggle-header .toggle-icon {
    transform: rotate(180deg);
}

/* 头部折叠按钮的悬停效果 */
.sidebar-toggle-header:hover .toggle-icon {
    transform: scale(1.1);
}

.sidebar.collapsed .sidebar-toggle-header:hover .toggle-icon {
    transform: rotate(180deg) scale(1.1);
}

.mobile-menu-btn {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: white;
    width: 36px;
    height: 36px;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    backdrop-filter: blur(10px);
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.header-title-group {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.header-left h1 {
    font-size: 1.2rem; /* 从1.3rem减少到1.2rem */
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.8rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin: 0;
    line-height: 1.2;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.status-indicator,
.version-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
}

.status-label,
.version-label {
    color: rgba(255, 255, 255, 0.8);
}

.status-value {
    background: var(--success-gradient);
    color: white;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all var(--transition-smooth);
}

.status-value:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.version-value {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.about-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    backdrop-filter: blur(10px);
}

.about-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* ==================== Header Book Selection ==================== */
.book-selection-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs) var(--spacing-sm);
    transition: all var(--transition-smooth);
}

.book-selection-header:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.book-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    margin: 0;
}

.book-controls-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.book-select-header {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    padding: 4px var(--spacing-xs);
    font-size: 0.8rem;
    color: white;
    min-width: 160px;
    max-width: 200px;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.book-select-header:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.book-select-header:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.25);
}

/* 修复书籍下拉菜单option元素的显示问题 */
.book-select-header option {
    background-color: #2d3748; /* 深色背景 */
    color: #ffffff; /* 白色文字 */
    padding: 8px 12px;
    border: none;
    font-size: 0.875rem;
}

/* 为不同浏览器提供兼容性支持 */
.book-select-header option:hover,
.book-select-header option:focus {
    background-color: #4a5568; /* 悬停时稍亮的背景 */
    color: #ffffff;
}

/* 选中状态的option样式 */
.book-select-header option:checked {
    background-color: #667eea; /* 主题色背景 */
    color: #ffffff;
    font-weight: 600;
}

.btn-header {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: rgba(255, 255, 255, 0.8);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-refresh:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    color: white;
    transform: rotate(180deg);
}
