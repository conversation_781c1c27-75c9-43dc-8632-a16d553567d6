/*
 * Sidebar - 侧边栏样式
 * 包含侧边栏容器、导航菜单、折叠状态等所有侧边栏相关样式
 * 支持桌面端折叠和移动端滑出效果
 */

/* ==================== Sidebar ==================== */
.sidebar {
    position: relative;
    width: 150px; /* 从180px进一步减少到150px */
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 800;
    transition: all var(--transition-smooth);
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.sidebar.collapsed {
    width: 60px;
}

.toggle-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-smooth);
    font-weight: bold;
    display: inline-block;
    line-height: 1;
}

.sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}

.sidebar-nav {
    padding: var(--spacing-sm) 0; /* 增加顶部padding，让菜单从顶部开始 */
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm); /* 从1rem减少到0.5rem */
    padding: var(--spacing-sm) var(--spacing-md); /* 从1rem 1.5rem减少到0.5rem 1rem */
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-smooth);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    margin-right: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateX(5px);
}

.nav-link:hover::before {
    transform: scaleY(1);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-weight: 600;
}

.nav-link.active::before {
    transform: scaleY(1);
}

.nav-icon {
    font-size: 1.3rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: transform var(--transition-smooth);
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 1;
    transition: opacity var(--transition-smooth);
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    pointer-events: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    align-items: center;
    padding: var(--spacing-sm); /* 减少padding，给图标更多空间 */
    margin: var(--spacing-xs) var(--spacing-xs); /* 减少左右margin */
    border-radius: var(--radius-lg);
    gap: 0;
    width: calc(60px - 2 * var(--spacing-xs)); /* 重新计算宽度：60px - 8px = 52px */
    height: 48px; /* 增加高度 */
    box-sizing: border-box;
    display: flex;
    position: relative; /* 确保定位正确 */
}

.sidebar.collapsed .nav-link::before {
    display: none;
}

/* 确保折叠状态下图标正确显示 */
.app-container .sidebar.collapsed .nav-link .nav-icon {
    font-size: 1.6rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    opacity: 1;
    visibility: visible;
    z-index: 10;
    margin: 0;
    padding: 0;
    line-height: 1;
    text-align: center;
    position: relative;
    overflow: visible;
    color: white;
}

/* 折叠状态下的悬停效果优化 */
.sidebar.collapsed .nav-link:hover .nav-icon {
    transform: scale(1.1); /* 适度的悬停缩放效果 */
    transition: transform var(--transition-smooth);
}

/* 确保折叠状态下nav-text完全隐藏 */
.app-container .sidebar.collapsed .nav-link .nav-text {
    display: none;
    width: 0;
    height: 0;
    overflow: hidden;
}

/* 桌面端专用的折叠样式 - 避免移动端干扰 */
@media (min-width: 769px) {
    .app-container .sidebar.collapsed {
        width: 60px;
    }

    .app-container .sidebar.collapsed .nav-link {
        width: 52px;
        min-width: 52px;
        max-width: 52px;
    }

    .app-container .sidebar.collapsed .nav-link .nav-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }
}

/* 调试样式 - 可以临时启用来检查布局 */
/*
.sidebar.collapsed .nav-link {
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid red !important;
}

.sidebar.collapsed .nav-icon {
    background: rgba(0, 255, 0, 0.2) !important;
    border: 1px solid green !important;
}
*/
