"""
Modern GUI App Template - Example Handler

示例业务处理器，展示如何实现具体的业务逻辑。
这个文件作为模板，您可以复制并修改为您的具体业务需求。

主要功能：
- 展示处理器的基本结构
- 演示参数验证和错误处理
- 提供可复用的代码模式

设计模式：
- 单一职责原则：每个函数只负责一个具体的业务操作
- 统一的输入输出格式
- 完善的错误处理和日志记录
"""

import time
from typing import Dict, Any, List
from pathlib import Path


def example_operation(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    示例业务操作。
    
    这是一个示例函数，展示如何实现具体的业务逻辑。
    您可以复制这个函数并修改为您的具体需求。
    
    Args:
        params (Dict[str, Any]): 操作参数，包含：
            - operation_type (str): 操作类型
            - data (Any): 操作数据
            - options (Dict, optional): 可选配置
    
    Returns:
        Dict[str, Any]: 操作结果，包含：
            - success (bool): 操作是否成功
            - data (Any): 返回数据
            - message (str): 操作消息
            - timestamp (str): 操作时间戳
    
    Raises:
        ValueError: 当参数无效时
        RuntimeError: 当操作失败时
    """
    try:
        # 1. 参数验证
        if not isinstance(params, dict):
            raise ValueError("参数必须是字典格式")
        
        operation_type = params.get("operation_type", "default")
        data = params.get("data")
        options = params.get("options", {})
        
        # 2. 根据操作类型执行不同的逻辑
        if operation_type == "process_data":
            result = _process_data(data, options)
        elif operation_type == "validate_input":
            result = _validate_input(data, options)
        elif operation_type == "generate_report":
            result = _generate_report(data, options)
        else:
            result = _default_operation(data, options)
        
        # 3. 返回成功结果
        return {
            "success": True,
            "data": result,
            "message": f"操作 '{operation_type}' 执行成功",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "operation_type": operation_type,
        }
    
    except ValueError as e:
        return {
            "success": False,
            "error": f"参数错误: {str(e)}",
            "message": "操作失败：参数验证不通过",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "操作失败：执行过程中出现错误",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }


def _process_data(data: Any, options: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理数据的内部函数。
    
    Args:
        data (Any): 要处理的数据
        options (Dict[str, Any]): 处理选项
    
    Returns:
        Dict[str, Any]: 处理结果
    """
    # 示例：数据处理逻辑
    if isinstance(data, str):
        processed_data = data.upper() if options.get("uppercase", False) else data.lower()
    elif isinstance(data, list):
        processed_data = sorted(data) if options.get("sort", False) else data
    elif isinstance(data, dict):
        processed_data = {k: v for k, v in data.items() if v is not None}
    else:
        processed_data = str(data)
    
    return {
        "original_data": data,
        "processed_data": processed_data,
        "processing_options": options,
        "data_type": type(data).__name__,
    }


def _validate_input(data: Any, options: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证输入数据的内部函数。
    
    Args:
        data (Any): 要验证的数据
        options (Dict[str, Any]): 验证选项
    
    Returns:
        Dict[str, Any]: 验证结果
    """
    validation_rules = options.get("rules", {})
    errors = []
    warnings = []
    
    # 示例验证规则
    if validation_rules.get("required", False) and not data:
        errors.append("数据不能为空")
    
    if validation_rules.get("min_length") and isinstance(data, str):
        min_len = validation_rules["min_length"]
        if len(data) < min_len:
            errors.append(f"字符串长度不能少于 {min_len} 个字符")
    
    if validation_rules.get("max_length") and isinstance(data, str):
        max_len = validation_rules["max_length"]
        if len(data) > max_len:
            warnings.append(f"字符串长度超过推荐的 {max_len} 个字符")
    
    if validation_rules.get("type") and not isinstance(data, validation_rules["type"]):
        errors.append(f"数据类型应该是 {validation_rules['type'].__name__}")
    
    is_valid = len(errors) == 0
    
    return {
        "is_valid": is_valid,
        "data": data,
        "errors": errors,
        "warnings": warnings,
        "validation_rules": validation_rules,
    }


def _generate_report(data: Any, options: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成报告的内部函数。
    
    Args:
        data (Any): 报告数据
        options (Dict[str, Any]): 报告选项
    
    Returns:
        Dict[str, Any]: 报告结果
    """
    report_type = options.get("type", "summary")
    include_details = options.get("include_details", False)
    
    # 示例报告生成逻辑
    if isinstance(data, list):
        report = {
            "total_items": len(data),
            "data_types": list(set(type(item).__name__ for item in data)),
            "sample_data": data[:3] if include_details else None,
        }
    elif isinstance(data, dict):
        report = {
            "total_keys": len(data),
            "keys": list(data.keys()) if include_details else None,
            "has_nested_objects": any(isinstance(v, (dict, list)) for v in data.values()),
        }
    else:
        report = {
            "data_type": type(data).__name__,
            "data_size": len(str(data)),
            "data_preview": str(data)[:100] if include_details else None,
        }
    
    return {
        "report_type": report_type,
        "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "report": report,
        "options": options,
    }


def _default_operation(data: Any, options: Dict[str, Any]) -> Dict[str, Any]:
    """
    默认操作的内部函数。
    
    Args:
        data (Any): 操作数据
        options (Dict[str, Any]): 操作选项
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    return {
        "operation": "default",
        "data": data,
        "options": options,
        "message": "执行了默认操作",
        "data_info": {
            "type": type(data).__name__,
            "size": len(str(data)),
            "is_empty": not bool(data),
        }
    }


# ==================== 其他示例函数 ====================

def batch_operation(items: List[Any], operation_func, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    批量操作函数示例。
    
    Args:
        items (List[Any]): 要处理的项目列表
        operation_func: 操作函数
        options (Dict[str, Any], optional): 操作选项
    
    Returns:
        Dict[str, Any]: 批量操作结果
    """
    if options is None:
        options = {}
    
    results = []
    errors = []
    
    for i, item in enumerate(items):
        try:
            result = operation_func(item, options)
            results.append({
                "index": i,
                "item": item,
                "result": result,
                "success": True,
            })
        except Exception as e:
            errors.append({
                "index": i,
                "item": item,
                "error": str(e),
                "success": False,
            })
    
    return {
        "total_items": len(items),
        "successful_items": len(results),
        "failed_items": len(errors),
        "results": results,
        "errors": errors,
        "success_rate": len(results) / len(items) if items else 0,
    }


def async_operation_template(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    异步操作模板（模拟）。
    
    在实际应用中，您可能需要使用 asyncio 来实现真正的异步操作。
    
    Args:
        params (Dict[str, Any]): 操作参数
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    import time
    
    # 模拟异步操作
    delay = params.get("delay", 1)
    time.sleep(delay)
    
    return {
        "operation": "async_simulation",
        "delay": delay,
        "completed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "params": params,
    }
