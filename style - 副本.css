/* Modern GUI App Template - Styles */
/* 基于 Augment-Code-Free 的现代化样式设计 */

/* ==================== CSS Variables ==================== */
:root {
    /* Modern Gradient Colors - 基于web/目录的设计风格 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-hover: #5a67d8;
    --primary-purple: #764ba2;
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --success-color: #48bb78;
    --success-hover: #38a169;
    --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --warning-color: #d97706;
    --error-color: #dc2626;
    --info-color: #0891b2;

    /* Glass Morphism Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-glass-secondary: rgba(248, 250, 252, 0.8);
    --bg-glass-hover: rgba(240, 248, 255, 0.95);

    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #4a5568;
    --text-inverse: #ffffff;

    /* Border Colors with Glass Effect */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;
    --border-glass: rgba(255, 255, 255, 0.2);
    --border-glass-hover: rgba(102, 126, 234, 0.3);

    /* Enhanced Shadows with Glass Effect */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-glass-hover: 0 12px 30px rgba(102, 126, 234, 0.15);
    --shadow-button: 0 4px 15px rgba(102, 126, 234, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(102, 126, 234, 0.4);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Enhanced Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-glass: 1rem;
    --radius-button: 0.5rem;

    /* Fonts */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Enhanced Transitions with Cubic Bezier */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Glass Effect Filters */
    --glass-blur: blur(20px);
    --glass-blur-light: blur(8px);

    /* Z-Index层级管理 */
    --z-dropdown: 10;
    --z-header: 900;
    --z-sidebar: 800;
    --z-sidebar-overlay: 1050;
    --z-sidebar-mobile: 1100;
    --z-modal-backdrop: 1050;
    --z-modal: 1100;
    --z-tooltip: 1200;
}

/* ==================== Reset & Base Styles ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
    line-height: 1.6;
    scroll-behavior: smooth;
}

/* ==================== Layout ==================== */
.app-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 中部布局容器 - 包含侧边栏和主内容区域 */
.layout-main {
    position: fixed;
    top: 60px; /* 头部高度 */
    bottom: 80px; /* 脚部高度 */
    left: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    z-index: 100;
}

/* ==================== Header ==================== */
.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 60px; /* 从80px减少到60px */
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-sm) var(--spacing-md); /* 从1rem 1.5rem减少到0.5rem 1rem */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    box-sizing: border-box;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 头部折叠按钮样式 */
.sidebar-toggle-header {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.sidebar-toggle-header:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.sidebar-toggle-header:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* 头部折叠按钮的波纹效果 */
.sidebar-toggle-header::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.sidebar-toggle-header:active::before {
    width: 40px;
    height: 40px;
}

.sidebar-toggle-header .toggle-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-smooth);
    font-weight: bold;
    display: inline-block;
    line-height: 1;
}

.sidebar.collapsed .sidebar-toggle-header .toggle-icon {
    transform: rotate(180deg);
}

/* 头部折叠按钮的悬停效果 */
.sidebar-toggle-header:hover .toggle-icon {
    transform: scale(1.1);
}

.sidebar.collapsed .sidebar-toggle-header:hover .toggle-icon {
    transform: rotate(180deg) scale(1.1);
}

.mobile-menu-btn {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: white;
    width: 36px;
    height: 36px;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    backdrop-filter: blur(10px);
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.header-title-group {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.header-left h1 {
    font-size: 1.2rem; /* 从1.3rem减少到1.2rem */
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.8rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin: 0;
    line-height: 1.2;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.status-indicator,
.version-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
}

.status-label,
.version-label {
    color: rgba(255, 255, 255, 0.8);
}

.status-value {
    background: var(--success-gradient);
    color: white;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all var(--transition-smooth);
}

.status-value:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.version-value {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.about-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    backdrop-filter: blur(10px);
}

.about-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* ==================== Header Book Selection ==================== */
.book-selection-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs) var(--spacing-sm);
    transition: all var(--transition-smooth);
}

.book-selection-header:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.book-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    margin: 0;
}

.book-controls-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.book-select-header {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    padding: 4px var(--spacing-xs);
    font-size: 0.8rem;
    color: white;
    min-width: 160px;
    max-width: 200px;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.book-select-header:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.book-select-header:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.25);
}

/* 修复书籍下拉菜单option元素的显示问题 */
.book-select-header option {
    background-color: #2d3748; /* 深色背景 */
    color: #ffffff; /* 白色文字 */
    padding: 8px 12px;
    border: none;
    font-size: 0.875rem;
}

/* 为不同浏览器提供兼容性支持 */
.book-select-header option:hover,
.book-select-header option:focus {
    background-color: #4a5568; /* 悬停时稍亮的背景 */
    color: #ffffff;
}

/* 选中状态的option样式 */
.book-select-header option:checked {
    background-color: #667eea; /* 主题色背景 */
    color: #ffffff;
    font-weight: 600;
}

.btn-header {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-smooth);
    color: rgba(255, 255, 255, 0.8);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-refresh:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    color: white;
    transform: rotate(180deg);
}

/* ==================== Sidebar ==================== */
.sidebar {
    position: relative;
    width: 150px; /* 从180px进一步减少到150px */
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 800;
    transition: all var(--transition-smooth);
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.sidebar.collapsed {
    width: 60px;
}





.toggle-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-smooth);
    font-weight: bold;
    display: inline-block;
    line-height: 1;
}

.sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}





.sidebar-nav {
    padding: var(--spacing-sm) 0; /* 增加顶部padding，让菜单从顶部开始 */
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm); /* 从1rem减少到0.5rem */
    padding: var(--spacing-sm) var(--spacing-md); /* 从1rem 1.5rem减少到0.5rem 1rem */
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-smooth);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    margin-right: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateX(5px);
}

.nav-link:hover::before {
    transform: scaleY(1);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-weight: 600;
}

.nav-link.active::before {
    transform: scaleY(1);
}

.nav-icon {
    font-size: 1.3rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: transform var(--transition-smooth);
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 1;
    transition: opacity var(--transition-smooth);
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    pointer-events: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    align-items: center;
    padding: var(--spacing-sm); /* 减少padding，给图标更多空间 */
    margin: var(--spacing-xs) var(--spacing-xs); /* 减少左右margin */
    border-radius: var(--radius-lg);
    gap: 0;
    width: calc(60px - 2 * var(--spacing-xs)); /* 重新计算宽度：60px - 8px = 52px */
    height: 48px; /* 增加高度 */
    box-sizing: border-box;
    display: flex;
    position: relative; /* 确保定位正确 */
}

.sidebar.collapsed .nav-link::before {
    display: none;
}

/* 确保折叠状态下图标正确显示 */
.app-container .sidebar.collapsed .nav-link .nav-icon {
    font-size: 1.6rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    opacity: 1;
    visibility: visible;
    z-index: 10;
    margin: 0;
    padding: 0;
    line-height: 1;
    text-align: center;
    position: relative;
    overflow: visible;
    color: white;
}

/* 折叠状态下的悬停效果优化 */
.sidebar.collapsed .nav-link:hover .nav-icon {
    transform: scale(1.1); /* 适度的悬停缩放效果 */
    transition: transform var(--transition-smooth);
}

/* 确保折叠状态下nav-text完全隐藏 */
.app-container .sidebar.collapsed .nav-link .nav-text {
    display: none;
    width: 0;
    height: 0;
    overflow: hidden;
}

/* 桌面端专用的折叠样式 - 避免移动端干扰 */
@media (min-width: 769px) {
    .app-container .sidebar.collapsed {
        width: 60px;
    }

    .app-container .sidebar.collapsed .nav-link {
        width: 52px;
        min-width: 52px;
        max-width: 52px;
    }

    .app-container .sidebar.collapsed .nav-link .nav-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }
}

/* 调试样式 - 可以临时启用来检查布局 */
/*
.sidebar.collapsed .nav-link {
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid red !important;
}

.sidebar.collapsed .nav-icon {
    background: rgba(0, 255, 0, 0.2) !important;
    border: 1px solid green !important;
}
*/

/* ==================== Main Content ==================== */
.main-content {
    flex: 1;
    padding: var(--spacing-sm);
    padding-top: var(--spacing-md); /* 减少顶部间距 */
    padding-bottom: var(--spacing-md); /* 脚部高度 + 适当间距 */
    overflow: hidden;
    height: 100%;
    box-sizing: border-box;
    transition: all var(--transition-smooth);
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 140px); /* 调整最小高度 */
}

/* 侧边栏折叠状态下主内容区域不需要特殊调整，因为使用flex布局自动处理 */

/* ==================== Welcome Panel ==================== */
.welcome-panel {
    margin-bottom: var(--spacing-xl);
}

.welcome-content {
    text-align: center;
    padding: var(--spacing-2xl);
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.welcome-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.welcome-features {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.feature-icon {
    font-size: 1.25rem;
}

.welcome-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.welcome-btn:hover {
    background: var(--primary-hover);
}

/* ==================== Dashboard ==================== */
.dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr;
    }
}

/* ==================== Panel Cards ==================== */
.panel-card {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-glass);
    border: 1px solid var(--border-glass);
    overflow: hidden;
    transition: all var(--transition-smooth);
    position: relative;
}

.panel-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: relative;
}

.panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-lg);
    right: var(--spacing-lg);
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

.panel-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.refresh-btn {
    background: none;
    border: none;
    font-size: 1.125rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    color: var(--text-secondary);
}

.refresh-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: rotate(180deg);
    color: var(--primary-color);
}

/* ==================== System Info ==================== */
.system-info {
    padding: var(--spacing-lg);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    background: var(--bg-glass);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.info-item:hover {
    transform: translateX(5px) translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
    background: var(--bg-glass-hover);
}

.info-item:hover::before {
    transform: scaleY(1);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.info-value {
    color: var(--text-muted);
    font-family: var(--font-mono);
    font-size: 0.8rem;
    word-break: break-all;
    line-height: 1.4;
    background: rgba(102, 126, 234, 0.05);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all var(--transition-smooth);
}

.info-item:hover .info-value {
    background: rgba(102, 126, 234, 0.1);
    color: var(--text-primary);
}

/* ==================== Operations ==================== */
.operations-grid {
    display: grid;
    gap: var(--spacing-lg);
    padding: var(--spacing-sm);
}

.operation-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.operation-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.operation-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
    background: var(--bg-glass-hover);
}

.operation-card:hover::before {
    transform: scaleY(1);
}

.operation-icon {
    font-size: 2rem;
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: var(--radius-lg);
    transition: all var(--transition-smooth);
}

.operation-card:hover .operation-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: scale(1.1) rotate(5deg);
}

.operation-content {
    flex: 1;
}

.operation-content h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.operation-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* ==================== Buttons ==================== */
.operation-btn {
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-button);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-smooth);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    min-width: 130px;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.operation-btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    box-shadow: var(--shadow-button);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.btn-secondary:hover {
    background: var(--secondary-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-hover) 0%, #2f855a 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* ==================== Results Panel ==================== */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    z-index: 1000;
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
}

.results-content {
    padding: var(--spacing-sm);
    max-height: 60vh;
    overflow-y: auto;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.close-btn:hover {
    background-color: var(--bg-tertiary);
}

/* ==================== Footer ==================== */
.app-footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-md) var(--spacing-xl);
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    font-size: 0.875rem;
}

.footer-info {
    color: var(--text-secondary);
}

.separator {
    margin: 0 var(--spacing-sm);
}

.footer-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-link {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.footer-link:hover {
    color: var(--primary-color);
    background-color: var(--bg-tertiary);
}

/* ==================== Modals ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-glass);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-smooth);
}

.modal-overlay.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-large {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-tertiary);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 70vh;
    overflow-y: auto;
}

/* ==================== About Modal ==================== */
.about-section {
    margin-bottom: var(--spacing-lg);
}

.about-section:last-child {
    margin-bottom: 0;
}

.about-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.about-section p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.link-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.link-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.link-btn:hover {
    background: var(--primary-hover);
}

.tech-stack {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tech-item {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

/* ==================== Config Modal ==================== */
.config-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.config-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.config-btn:hover {
    background: var(--primary-hover);
}

.config-editor {
    width: 100%;
}

#configEditor {
    width: 100%;
    height: 300px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    resize: vertical;
    background: var(--bg-secondary);
}

#configEditor:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* ==================== Loading ==================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: var(--glass-blur);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

/* ==================== Utility Classes ==================== */
.loading {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-lg);
}

.error {
    color: var(--error-color);
}

.success {
    color: var(--success-color);
}

.warning {
    color: var(--warning-color);
}

.text-center {
    text-align: center;
}

.hidden {
    display: none !important;
}

/* ==================== Sidebar Responsive Design ==================== */
@media (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .layout-main {
        flex-direction: row;
    }

    .sidebar {
        position: absolute;
        transform: translateX(-100%);
        width: 200px; /* 从240px减少到200px，保持与桌面端的比例协调 */
        z-index: 1100;
        height: 100%;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 移动端不使用折叠状态，而是使用mobile-open状态 */
    .sidebar.collapsed {
        transform: translateX(-100%);
        width: 200px;
    }

    .main-content {
        flex: 1;
        width: 100%;
    }

    /* 移动端遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1050;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-smooth);
        backdrop-filter: blur(4px); /* 添加模糊效果 */
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* 移动端导航项优化 */
    .nav-link {
        padding: var(--spacing-lg) var(--spacing-xl); /* 增大触摸区域 */
        font-size: 1rem; /* 稍微增大字体 */
    }

    .nav-icon {
        font-size: 1.4rem; /* 增大图标 */
        width: 28px;
        height: 28px;
    }


}

/* ==================== Responsive Design ==================== */
@media (max-width: 768px) {
    .app-header {
        height: auto;
        min-height: 60px; /* 从80px减少到60px */
        padding: var(--spacing-xs) var(--spacing-sm); /* 进一步减少移动端padding */
    }

    .mobile-menu-btn {
        display: flex;
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .header-left {
        align-items: flex-start;
        width: 100%;
    }

    .header-title-group {
        flex: 1;
        text-align: center;
    }

    .header-right {
        flex-direction: column;
        gap: var(--spacing-xs);
        width: 100%;
    }

    .book-selection-header {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-sm);
    }

    .book-select-header {
        min-width: 150px;
        max-width: 200px;
        flex: 1;
    }

    .welcome-features {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .operation-card {
        flex-direction: column;
        text-align: center;
    }

    .config-actions {
        flex-direction: column;
    }

    .link-buttons {
        flex-direction: column;
    }
}

/* ==================== Config Panel Enhancements ==================== */
.config-panel {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-glass);
    border: 1px solid var(--border-glass);
    padding: 0; /* 从1rem减少到0rem */
    margin-bottom: var(--spacing-md); /* 从1.5rem减少到1rem */
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.config-panel:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
}





.config-panel h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}











/* ==================== Fixed Header Enhancements ==================== */
/* 确保固定头部在所有浏览器中正常工作 */
.app-header {
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

/* 为可能的长内容区域添加额外的滚动优化 */
.tab-content {
    scroll-margin-top: 100px; /* 确保锚点跳转时不被头部遮挡 */
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 允许flex子项收缩 */
    overflow: hidden;
}

/* 确保模态框在固定头部之上 */
.modal {
    z-index: 1100;
}

.modal-backdrop {
    z-index: 1050;
}

/* 为操作日志等可能很长的内容添加优化 */
.log-container {
    scroll-margin-top: 100px;
}

/* 确保下拉菜单在固定头部之下但在其他内容之上 */
select {
    position: relative;
    z-index: 10;
}

/* 通用select option样式 - 确保所有下拉菜单都有良好的对比度 */
select option {
    background-color: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1a202c);
    padding: 8px 12px;
    border: none;
    font-size: inherit;
}

select option:hover,
select option:focus {
    background-color: var(--bg-secondary, #f7fafc);
    color: var(--text-primary, #1a202c);
}

select option:checked {
    background-color: var(--primary-color, #667eea);
    color: #ffffff;
    font-weight: 600;
}

/* ==================== Pagination Styles ==================== */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    margin-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-size-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-pages {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.pagination-page {
    min-width: 2rem;
    height: 2rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.pagination-page:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-ellipsis {
    padding: 0 var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-jump {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-jump-input {
    width: 3rem;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    text-align: center;
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.pagination-jump-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
}

/* 响应式分页设计 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .pagination-info {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-jump {
        justify-content: center;
    }

    .pagination-pages {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .pagination-page {
        min-width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }

    .pagination-jump-input {
        width: 2.5rem;
    }

    .book-selection-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
    }

    .book-controls-header {
        width: 100%;
        justify-content: center;
    }

    .book-select-header {
        min-width: 120px;
        max-width: 160px;
        font-size: 0.8rem;
    }

    .book-label {
        font-size: 0.8rem;
    }
}

/* ==================== Footer ==================== */
.app-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-sm) 0;
    z-index: 900;
    overflow: hidden;
    box-sizing: border-box;
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.app-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
}

/* 确保脚部在滚动时保持固定 */
.app-footer {
    /* 防止在某些浏览器中出现滚动问题 */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    align-items: center;
    position: relative;
    flex-wrap: wrap;
    height: 100%;
}

.footer-warning, .footer-tip {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-inverse);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-smooth);
    backdrop-filter: blur(10px);
    flex: 1;
    min-width: 280px;
    max-width: 450px;
}

.footer-warning:hover, .footer-tip:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.footer-icon {
    font-size: 1.3rem;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    flex-shrink: 0;
    transition: all var(--transition-smooth);
}

.footer-warning:hover .footer-icon, .footer-tip:hover .footer-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.footer-text {
    flex: 1;
}

.footer-text p {
    margin: 0;
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1.3;
    opacity: 0.95;
}

/* Footer 响应式设计 */
@media (max-width: 768px) {
    .app-footer {
        height: auto;
        min-height: 80px;
        padding: var(--spacing-sm) 0;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
        padding: 0 var(--spacing-md);
    }

    .footer-warning, .footer-tip {
        padding: var(--spacing-sm) var(--spacing-md);
        gap: var(--spacing-sm);
        min-width: auto;
        max-width: none;
    }

    .footer-icon {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }

    .footer-text p {
        font-size: 0.85rem;
    }



    .main-content {
        padding: var(--spacing-xs); /* 移动端使用更小的padding */
        padding-top: var(--spacing-sm); /* 减少顶部间距 */
        padding-bottom: calc(80px + var(--spacing-sm)); /* 脚部高度 + 小间距 */
        min-height: calc(100vh - 140px); /* 调整移动端最小高度 */
    }

    /* 移动端面板优化 */
    .config-panel {
        padding: var(--spacing-sm); /* 移动端也使用0padding */
        margin-bottom: var(--spacing-sm); /* 减少面板间距 */
    }
}

/* ==================== 映射页面布局 ==================== */
.mapping-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.mapping-header h3 {
    margin: 0;
    flex: 1;
    min-width: 200px;
}

.mapping-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}

.mapping-actions .btn {
    white-space: nowrap;
    transition: all var(--transition-smooth);
}

.action-buttons-group {
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all var(--transition-smooth);
}

.action-buttons-group.hidden {
    display: none;
}

/* 文件信息显示区域 */
.file-info-section {
    background: var(--bg-glass-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.file-info-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.file-info-label {
    font-weight: 600;
    color: var(--text-primary);
}

.file-name {
    font-weight: 500;
    color: var(--primary-color);
}

.file-path {
    color: var(--text-secondary);
    font-size: 0.9em;
    flex: 1;
    min-width: 200px;
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
    margin-left: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .mapping-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .mapping-header h3 {
        text-align: center;
        min-width: auto;
    }

    .mapping-actions {
        width: 100%;
        justify-content: center;
    }

    .mapping-actions .btn {
        flex: 1;
        min-width: 0;
    }

    .action-buttons-group {
        width: 100%;
        justify-content: center;
    }

    .action-buttons-group .btn {
        flex: 1;
        min-width: 0;
    }

    .file-info-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .file-path {
        min-width: auto;
        word-break: break-all;
    }

    .btn-sm {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* ==================== 标签页显示修复 ==================== */
.tab-content {
    display: none !important; /* 确保默认隐藏 */
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.tab-content.active {
    display: flex !important; /* 确保激活时显示 */
}

/* ==================== Z-Index层级修复 ==================== */
.app-header {
    z-index: var(--z-header);
}

.sidebar {
    z-index: var(--z-sidebar);
}

.sidebar-overlay {
    z-index: var(--z-sidebar-overlay);
}

.modal-overlay {
    z-index: var(--z-modal);
}

.modal-backdrop {
    z-index: var(--z-modal-backdrop);
}

select {
    position: relative;
    z-index: var(--z-dropdown);
}

/* 移动端侧边栏层级 */
@media (max-width: 768px) {
    .sidebar {
        z-index: var(--z-sidebar-mobile);
    }
}

/* ==================== 侧边栏折叠修复 ==================== */
/* 注意：主要的折叠样式已在前面定义，这里只保留必要的补充样式 */
.app-container .sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-sm);
    margin: var(--spacing-xs);
    width: 52px;
    height: 48px;
    gap: 0;
    border-radius: var(--radius-md);
}

/* 桌面端折叠宽度已在前面定义，此处移除重复样式 */

/* ==================== 移动端遮罩层修复 ==================== */
@media (max-width: 768px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: var(--z-sidebar-overlay);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-smooth);
        backdrop-filter: blur(4px);
        pointer-events: none;
    }

    .app-container .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
    }

    .app-container .sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 确保侧边栏在移动端的正确定位 */
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 200px;
        transform: translateX(-100%);
        transition: transform var(--transition-smooth);
    }
}

/* ==================== JavaScript动画状态修复 ==================== */
.toggle-icon {
    transition: transform var(--transition-smooth);
    display: inline-block;
    line-height: 1;
    transform-origin: center;
}

.app-container .toggle-icon.js-animating {
    transform: scale(1.2);
}

.app-container .toggle-icon.js-collapsed {
    transform: rotate(180deg);
}

.app-container .toggle-icon.js-collapsed.js-animating {
    transform: rotate(180deg) scale(1.2);
}

/* 按钮悬停效果优化 */
.app-container .btn-refresh:hover .toggle-icon {
    transform: rotate(180deg);
}

/* ==================== 表单样式 ==================== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
}

/* 表单行样式 */
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}

.form-row .form-group input,
.form-row .form-group select {
    width: 100%;
}

/* ==================== 按钮样式增强 ==================== */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-button);
    cursor: pointer;
    margin-right: var(--spacing-sm);
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    box-shadow: var(--shadow-button);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.btn-secondary:hover {
    background: var(--secondary-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-hover) 0%, #2f855a 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* ==================== Message Container ==================== */
#messageContainer {
    position: fixed;
    top: 80px; /* 头部高度 + 间距 */
    right: var(--spacing-md);
    width: 400px;
    max-width: calc(100vw - 2 * var(--spacing-md));
    z-index: 1200; /* 高于脚部(900)和头部(1000) */
    pointer-events: none; /* 允许点击穿透到下层元素 */
}

.alert {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    backdrop-filter: var(--glass-blur);
    box-shadow: var(--shadow-glass);
    pointer-events: auto; /* 恢复消息本身的点击事件 */
    animation: slideInRight 0.3s ease-out;
    transition: all var(--transition-smooth);
}

.alert-success {
    border-left: 4px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.alert-warning {
    border-left: 4px solid #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.alert-error {
    border-left: 4px solid #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.alert-info {
    border-left: 4px solid #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 移动端消息容器适配 */
@media (max-width: 768px) {
    #messageContainer {
        top: 100px; /* 移动端头部更高 */
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        width: auto;
        max-width: none;
    }
    
    .alert {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(217, 119, 6, 0.2);
}

.btn-warning:hover {
    background: #b45309;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
}

/* ==================== 标签页样式 ==================== */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-glass);
    margin-bottom: var(--spacing-lg);
    background: var(--bg-glass);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    padding: var(--spacing-sm);
}

.tab {
    padding: var(--spacing-sm);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-smooth);
    font-weight: 500;
    color: var(--text-secondary);
    position: relative;
}

.tab:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

/* ==================== 进度条样式 ==================== */
.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-sm) 0;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ==================== 操作日志页面样式 ==================== */
.logs-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.logs-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.logs-actions {
    display: flex;
    align-items: center;
}

/* 操作日志容器样式 */
.logs-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.logs-content-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 优化后的日志面板样式 */
.log-panel {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-sm);
    font-family: var(--font-mono);
    font-size: 0.75rem;
    line-height: 1.4;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Webkit浏览器滚动条样式 */
.log-panel::-webkit-scrollbar {
    width: 8px;
}

.log-panel::-webkit-scrollbar-track {
    background: transparent;
}

.log-panel::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.log-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 日志条目样式 */
.log-panel div {
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-sm);
    color: var(--text-secondary);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-panel p {
    margin: 0;
    padding: var(--spacing-sm);
    color: var(--text-secondary);
}

/* ==================== CV简名管理页面样式 ==================== */
.nicknames-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.nicknames-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.nicknames-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.nicknames-actions {
    display: flex;
    align-items: center;
}

.nicknames-input-section {
    flex-shrink: 0;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
}

.nicknames-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    width: 100%;
}

.nicknames-content-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    padding: var(--spacing-sm);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.nicknames-content-wrapper::-webkit-scrollbar {
    width: 8px;
}

.nicknames-content-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

/* ==================== 分配任务页面样式 ==================== */
.assignment-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.assignment-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.assignment-config-section {
    flex-shrink: 0;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
}

.assignment-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    width: 100%;
}

.assignment-content-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    padding: var(--spacing-sm);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.assignment-content-wrapper::-webkit-scrollbar {
    width: 8px;
}

.assignment-content-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

/* ==================== 文件上传样式 ==================== */
.file-upload {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-smooth);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur-light);
    position: relative;
    overflow: hidden;
}

.file-upload::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.8s ease;
}

.file-upload:hover {
    border-color: var(--primary-color);
    background: var(--bg-glass-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.file-upload:hover::before {
    left: 100%;
}

.file-upload.dragover {
    border-color: var(--primary-color);
    background: var(--bg-glass-hover);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* ==================== 警告和提示样式 ==================== */
.alert {
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    margin: var(--spacing-md) 0;
    backdrop-filter: var(--glass-blur-light);
    border-left: 4px solid;
    transition: all var(--transition-smooth);
}

.alert:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: rgba(212, 237, 218, 0.9);
    color: #155724;
    border-color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-error {
    background: rgba(248, 215, 218, 0.9);
    color: #721c24;
    border-color: var(--error-color);
    border-left-color: var(--error-color);
}

.alert-warning {
    background: rgba(255, 243, 205, 0.9);
    color: #856404;
    border-color: var(--warning-color);
    border-left-color: var(--warning-color);
}

/* ==================== 设置页面样式 ==================== */
.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
}

.settings-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.settings-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.settings-actions .btn {
    margin-right: 0;
}

/* 设置TAB导航样式 */
.settings-tabs {
    display: flex;
    background: var(--bg-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-glass);
    box-shadow: var(--shadow-sm);
}

.settings-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    font-weight: 500;
    color: var(--text-secondary);
    position: relative;
    background: transparent;
}

.settings-tab:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.settings-tab.active {
    color: var(--text-inverse);
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.settings-tab .tab-icon {
    font-size: 1.1rem;
}

.settings-tab .tab-text {
    font-size: 0.875rem;
    font-weight: 600;
}

/* 设置TAB内容区域样式 */
.settings-tab-content {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.settings-tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    z-index: 1;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-sm);
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    background: transparent;
    color: inherit;
    height: 100%;
    width: 100%;
}

.settings-tab-pane.active {
    opacity: 1;
    visibility: visible;
    z-index: 2;
}

/* Webkit浏览器滚动条样式 */
.settings-tab-pane::-webkit-scrollbar {
    width: 8px;
}

.settings-tab-pane::-webkit-scrollbar-track {
    background: transparent;
}

.settings-tab-pane::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.settings-tab-pane::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 设置区块样式 */
.settings-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-smooth);
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
}

.settings-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.settings-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 700;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
    position: relative;
}

.settings-section h4::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30%;
    height: 2px;
    background: var(--primary-gradient);
}

.settings-section .form-group {
    margin-bottom: var(--spacing-md);
}

.settings-section .form-group:last-child {
    margin-bottom: 0;
}

.settings-section input[type="text"],
.settings-section input[type="password"],
.settings-section input[type="number"] {
    width: 300px;
    margin-right: var(--spacing-sm);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    transition: all var(--transition-smooth);
    position: relative;
    z-index: 1;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--text-primary);
}

.settings-section input[type="text"]:focus,
.settings-section input[type="password"]:focus,
.settings-section input[type="number"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-glass-hover);
}

.settings-section small {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* ==================== CV列表页面样式 ==================== */
.cv-list-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-sm);
    overflow: hidden;
    box-sizing: border-box;
}

.cv-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.cv-list-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.cv-list-actions {
    display: flex;
    align-items: center;
}

.cv-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.cv-list-table-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 固定表头的表格样式 */
.cv-list-table-wrapper .mapping-table {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0;
    border-radius: 0;
    border: none;
}

.cv-list-table-wrapper .mapping-table thead {
    flex-shrink: 0;
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.cv-list-table-wrapper .mapping-table thead th {
    padding: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
}

.cv-list-table-wrapper .mapping-table tbody {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar {
    width: 8px;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
    background: transparent;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.cv-list-table-wrapper .mapping-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.cv-list-table-wrapper .mapping-table tbody td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    vertical-align: middle;
}

/* 固定分页控件样式 */
.cv-list-pagination-fixed {
    flex-shrink: 0;
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    position: relative;
    z-index: 5;
    width: 100%;
    box-sizing: border-box;
}

/* ==================== 角色-CV映射表页面样式 ==================== */
.mapping-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.mapping-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.mapping-table-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 固定表头的映射表格样式 */
.mapping-table-wrapper .mapping-table {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0;
    border-radius: 0;
    border: none;
}

.mapping-table-wrapper .mapping-table thead {
    flex-shrink: 0;
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.mapping-table-wrapper .mapping-table thead th {
    padding: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
}

.mapping-table-wrapper .mapping-table tbody {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar {
    width: 8px;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
    background: transparent;
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.mapping-table-wrapper .mapping-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.mapping-table-wrapper .mapping-table tbody td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    vertical-align: middle;
}

/* 固定分页控件样式 */
.mapping-pagination-fixed {
    flex-shrink: 0;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    position: relative;
    z-index: 5;
    width: 100%;
    box-sizing: border-box;
}

/* 空状态样式 */
.mapping-table-wrapper p,
.cv-list-table-wrapper p {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    margin: 0;
    font-style: italic;
}

/* 映射表格样式增强（保持向后兼容） */
.mapping-table tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.mapping-table tr:hover {
    background: var(--bg-glass-hover);
}

.mapping-table tr:hover td {
    background: transparent;
}

/* ==================== 通用工具类 ==================== */
/* 统一的显示/隐藏控制类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.flex-visible {
    display: flex !important;
}

.inline-visible {
    display: inline !important;
}

.inline-block-visible {
    display: inline-block !important;
}

/* 输入框状态样式 */
.input-error {
    color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.input-success {
    color: #059669 !important;
    border-color: #059669 !important;
}

/* ==================== 响应式设计优化 ==================== */
/* 操作日志响应式设计 */
@media (max-width: 768px) {
    .logs-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .logs-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .logs-panel {
        padding: var(--spacing-sm);
    }

    .log-panel {
        font-size: 0.7rem;
        padding: var(--spacing-sm);
    }

    /* 设置页面响应式设计 */
    .settings-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .settings-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .settings-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .settings-tab {
        width: 100%;
        justify-content: center;
    }

    .settings-tab-pane {
        padding: var(--spacing-sm);
    }

    .settings-section {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-sm);
    }

    .settings-section input[type="text"],
    .settings-section input[type="password"],
    .settings-section input[type="number"] {
        width: 100%;
        margin-right: 0;
        margin-bottom: var(--spacing-xs);
    }

    /* CV列表响应式设计 */
    .cv-list-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .cv-list-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* 映射表格响应式设计 */
    .mapping-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .mapping-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
