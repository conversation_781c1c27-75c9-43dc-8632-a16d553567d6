"""
配置服务

此模块提供了配置管理服务，封装了对配置文件的操作。
作为处理器层与数据层之间的桥梁。
"""
from typing import Any, Dict
from .json_config_manager import json_config_manager, JsonConfigManager


class ConfigService:
    """配置服务类

    封装了对配置文件的操作，提供了获取和设置配置项的方法。

    Attributes:
        config_manager: 配置管理器实例
    """

    def __init__(self, config_file_path: str = None):
        """初始化配置服务

        Args:
            config_file_path: 配置文件路径，如果提供则创建新的JsonConfigManager实例，
                            否则使用全局json_config_manager实例
        """
        if config_file_path:
            # 当提供配置路径时，创建新的JsonConfigManager实例
            self.config_manager = JsonConfigManager(config_file_path)
        else:
            # 当未提供路径时，使用现有的全局json_config_manager实例
            self.config_manager = json_config_manager
    
    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """获取配置项
        
        Args:
            section: 配置节
            key: 配置键
            fallback: 默认值
            
        Returns:
            str: 配置项的值
        """
        return self.config_manager.get(section, key, fallback)
    
    def set(self, section: str, key: str, value: str) -> None:
        """设置配置项
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
        """
        self.config_manager.set(section, key, value)
    
    def save(self) -> None:
        """保存配置"""
        self.config_manager.save()
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置
        
        Returns:
            Dict[str, Any]: 完整的配置字典
        """
        return self.config_manager.get_all_config()
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """更新配置
        
        Args:
            new_config: 新的配置字典
        """
        self.config_manager.update_config(new_config)
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置
        
        Returns:
            Dict[str, Any]: API配置字典
        """
        return {
            'base_url': self.get('API', 'base_url'),
            'default_token': self.get('API', 'default_token'),
            'enable_real_assignment': self.get('API', 'enable_real_assignment', 'false').lower() == 'true'
        }
    
    def get_files_config(self) -> Dict[str, Any]:
        """获取文件配置
        
        Returns:
            Dict[str, Any]: 文件配置字典
        """
        return {
            'cv_config_file': self.get('FILES', 'cv_config_file', 'config/cv_nicknames.json'),
            'last_excel_directory': self.get('FILES', 'last_excel_directory', '')
        }
    
    def get_excel_config(self) -> Dict[str, Any]:
        """获取Excel配置

        Returns:
            Dict[str, Any]: Excel配置字典
        """
        return {
            'character_sheet_name': self.get('EXCEL', 'character_sheet_name', '角色'),
            'character_column_index': self.get('EXCEL', 'character_column_index', 0),
            'cv_column_index': self.get('EXCEL', 'cv_column_index', 6),
            'price_column_index': self.get('EXCEL', 'price_column_index', 2),
            'character_column_keyword': self.get('EXCEL', 'character_column_keyword', '角色名称'),
            'cv_column_keyword': self.get('EXCEL', 'cv_column_keyword', '主播'),
            'price_column_keyword': self.get('EXCEL', 'price_column_keyword', '主播价格'),
            'header_row': self.get('EXCEL', 'header_row', 1),
            'sheet_name': self.get('EXCEL', 'character_sheet_name', '角色')  # 添加sheet_name映射
        }


