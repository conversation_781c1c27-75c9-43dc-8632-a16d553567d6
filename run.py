#!/usr/bin/env python3
"""
Modern GUI App Template - Cross-Platform Launcher

跨平台启动脚本，基于 Augment-Code-Free 的启动器设计。
支持Windows、macOS、Linux的统一启动体验。

主要功能：
- 自动检测操作系统平台
- 智能虚拟环境管理
- UV和pip的自动切换
- 友好的错误提示和解决建议
- 开发和生产环境支持

使用方法：
    python run.py              # 标准启动
    python run.py --dev         # 开发模式
    python run.py --debug       # 调试模式
    python run.py --no-venv     # 不使用虚拟环境
    python run.py --help        # 显示帮助

环境要求：
- Python 3.10+
- 已安装项目依赖
- 可选：UV包管理器（推荐）
"""

import argparse
import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Optional, Tuple

class LauncherError(Exception):
    """启动器错误"""
    pass

class uvModernGUILauncher:
    """Modern GUI App启动器"""
    
    def __init__(self, args):
        self.args = args
        self.project_root = Path(__file__).parent
        self.src_dir = self.project_root / "src"
        
        # 平台检测
        self.platform = self._detect_platform()
        
        # 虚拟环境路径
        self.venv_dir = self.project_root / ".venv"
        
        print(f"🚀 Modern GUI App 启动器")
        print(f"🖥️  平台: {self.platform}")
        print(f"📁 项目目录: {self.project_root}")
        
        if self.args.debug:
            print(f"🐛 调试模式已启用")
        elif self.args.dev:
            print(f"🔧 开发模式已启用")
    
    def _detect_platform(self) -> str:
        """检测操作系统平台"""
        system = platform.system().lower()
        if system == "windows":
            return "Windows"
        elif system == "darwin":
            return "macOS"
        elif system == "linux":
            return "Linux"
        else:
            return f"Unknown ({system})"
    
    def _check_python_version(self):
        """检查Python版本"""
        version = sys.version_info
        if version < (3, 10):
            raise LauncherError(
                f"需要Python 3.10或更高版本，当前版本: {version.major}.{version.minor}"
            )
        
        if self.args.debug:
            print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    def _check_uv_available(self) -> bool:
        """检查UV是否可用"""
        try:
            result = subprocess.run(
                ["uv", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                if self.args.debug:
                    print(f"✅ UV可用: {result.stdout.strip()}")
                return True
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass
        
        if self.args.debug:
            print("⚠️  UV不可用，将使用pip")
        return False
    
    def _get_venv_paths(self) -> Tuple[Path, Path]:
        """获取虚拟环境路径"""
        if platform.system() == "Windows":
            scripts_dir = self.venv_dir / "Scripts"
            python_exe = scripts_dir / "python.exe"
            activate_script = scripts_dir / "activate.bat"
        else:
            bin_dir = self.venv_dir / "bin"
            python_exe = bin_dir / "python"
            activate_script = bin_dir / "activate"
        
        return python_exe, activate_script
    
    def _check_virtual_environment(self) -> bool:
        """检查虚拟环境"""
        if self.args.no_venv:
            if self.args.debug:
                print("⏭️  跳过虚拟环境检查（--no-venv）")
            return False
        
        python_exe, activate_script = self._get_venv_paths()
        
        if not self.venv_dir.exists():
            print("❌ 虚拟环境不存在")
            self._suggest_environment_setup()
            return False
        
        if not python_exe.exists():
            print("❌ 虚拟环境Python解释器不存在")
            self._suggest_environment_setup()
            return False
        
        if self.args.debug:
            print(f"✅ 虚拟环境: {self.venv_dir}")
            print(f"✅ Python解释器: {python_exe}")
        
        return True
    
    def _suggest_environment_setup(self):
        """建议环境设置方法"""
        print("\n💡 环境设置建议:")
        
        if self._check_uv_available():
            print("   uv sync                    # 使用UV创建环境并安装依赖")
        else:
            print("   python -m venv .venv       # 创建虚拟环境")
            if platform.system() == "Windows":
                print("   .venv\\Scripts\\activate    # 激活虚拟环境")
            else:
                print("   source .venv/bin/activate  # 激活虚拟环境")
            print("   pip install -r requirements.txt  # 安装依赖")
        
        print("\n🔧 或者运行设置脚本:")
        if platform.system() == "Windows":
            print("   .\\setup-dev.ps1           # Windows PowerShell")
        else:
            print("   ./setup-dev.sh             # Linux/macOS")
    
    def _check_dependencies(self):
        """检查项目依赖"""
        if self.args.debug:
            print("📦 检查项目依赖...")
        
        # 检查主模块是否存在
        main_module = self.src_dir / "assign_cv_to_characters" / "main.py"
        if not main_module.exists():
            raise LauncherError(f"主模块不存在: {main_module}")
        
        # 检查关键依赖
        try:
            # 使用当前Python解释器进行依赖检查
            python_cmd = sys.executable

            # 检查pywebview
            result = subprocess.run([
                python_cmd, "-c", "import pywebview; print(pywebview.__version__)"
            ], capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                print("❌ pywebview未安装或不可用")
                self._suggest_dependency_installation()
                raise LauncherError("缺少必要依赖")

            if self.args.debug:
                print(f"✅ pywebview版本: {result.stdout.strip()}")
        
        except subprocess.TimeoutExpired:
            raise LauncherError("依赖检查超时")
        except Exception as e:
            if self.args.debug:
                print(f"⚠️  依赖检查失败: {e}")
    
    def _suggest_dependency_installation(self):
        """建议依赖安装方法"""
        print("\n💡 依赖安装建议:")
        
        if self._check_uv_available():
            print("   uv sync                    # 安装所有依赖")
            if self.args.dev:
                print("   uv sync --extra dev        # 包含开发依赖")
        else:
            print("   pip install -r requirements.txt  # 安装依赖")
            if self.args.dev:
                print("   pip install -e .[dev]     # 包含开发依赖")
    
    def _build_launch_command(self) -> List[str]:
        """构建启动命令"""
        if self.args.debug:
            print("🔧 构建启动命令...")
        
        # 使用当前Python解释器
        python_cmd = sys.executable
        
        # 构建基础命令
        cmd = [python_cmd, "-m", "assign_cv_to_characters"]
        
        # 添加模式参数
        if self.args.debug:
            cmd.extend(["--debug"])
        elif self.args.dev:
            cmd.extend(["--dev"])
        
        if self.args.debug:
            print(f"🚀 启动命令: {' '.join(cmd)}")
        
        return cmd
    
    def _launch_application(self):
        """启动应用程序"""
        print("🌟 启动 Modern GUI App...")
        
        cmd = self._build_launch_command()
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            
            # 开发模式的额外环境变量
            if self.args.dev:
                env["MODERN_GUI_DEV_MODE"] = "1"

            if self.args.debug:
                env["MODERN_GUI_DEBUG"] = "1"

            # 设置PYTHONPATH以确保模块能正确导入
            env["PYTHONPATH"] = str(self.src_dir)
            
            # 启动应用
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                env=env
            )
            
            print("✅ 应用已启动")
            print("💡 关闭应用窗口以退出程序")
            
            # 等待应用结束
            return_code = process.wait()
            
            if return_code == 0:
                print("👋 应用正常退出")
            else:
                print(f"⚠️  应用异常退出，返回码: {return_code}")
                return return_code
        
        except KeyboardInterrupt:
            print("\n⚠️  用户中断应用启动")
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
            return 1
        
        except Exception as e:
            raise LauncherError(f"启动应用失败: {e}")
        
        return 0
    
    def run(self) -> int:
        """运行启动器"""
        try:
            self._check_python_version()
            self._check_dependencies()
            return self._launch_application()
        
        except LauncherError as e:
            print(f"❌ 启动失败: {e}")
            return 1
        except KeyboardInterrupt:
            print("\n⚠️  启动被用户中断")
            return 1
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            if self.args.debug:
                import traceback
                traceback.print_exc()
            return 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Modern GUI App 跨平台启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--dev",
        action="store_true",
        help="开发模式启动"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="调试模式启动（包含详细输出）"
    )
    
    parser.add_argument(
        "--no-venv",
        action="store_true",
        help="不使用虚拟环境"
    )
    
    args = parser.parse_args()
    
    launcher = uvModernGUILauncher(args)
    return launcher.run()

if __name__ == "__main__":
    sys.exit(main())
