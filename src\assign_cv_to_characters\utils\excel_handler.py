"""
Excel处理工具

此模块提供了Excel文件读写的工具函数。
"""
import pandas as pd
from typing import Dict, List, Any, Optional
import os
import warnings
import logging

# 抑制 openpyxl 的警告
warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl")


class ExcelHandler:
    """Excel处理工具类

    提供Excel文件读写的静态方法。
    """

    @staticmethod
    def _find_column_by_keyword(df: pd.DataFrame, keyword: str) -> Optional[int]:
        """根据关键词查找列索引

        Args:
            df: DataFrame对象
            keyword: 要查找的关键词

        Returns:
            Optional[int]: 找到的列索引，如果没找到则返回None
        """
        if df.empty or keyword is None:
            return None

        # 获取列名
        columns = df.columns.tolist()

        # 精确匹配
        for i, col in enumerate(columns):
            if str(col).strip() == keyword:
                return i

        # 包含匹配
        for i, col in enumerate(columns):
            if keyword in str(col):
                return i

        return None

    @staticmethod
    def read_character_cv_map(file_path: str, sheet_name: str,
                             header_row: int, character_col_idx: int,
                             cv_col_idx: int, character_keyword: str = None,
                             cv_keyword: str = None, price_col_idx: int = None,
                             price_keyword: str = None) -> Dict[str, str]:
        """从Excel读取角色-CV映射

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称
            header_row: 表头行号
            character_col_idx: 角色列索引（回退使用）
            cv_col_idx: CV列索引（回退使用）
            character_keyword: 角色列关键词（优先使用）
            cv_keyword: CV列关键词（优先使用）
            price_col_idx: 价格列索引（回退使用，可选）
            price_keyword: 价格列关键词（优先使用，可选）

        Returns:
            Dict[str, str]: 角色名称到CV信息的映射字典
                          - 不包含价格列时: {'角色名': 'CV名'}
                          - 包含价格列时: {'角色名': 'CV名|价格'}
        """
        if not file_path or not os.path.exists(file_path):
            return {}

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)

            # 确定实际使用的列索引
            actual_character_col_idx = character_col_idx
            actual_cv_col_idx = cv_col_idx
            actual_price_col_idx = price_col_idx

            # 如果提供了关键词，尝试根据关键词查找列索引
            if character_keyword:
                found_idx = ExcelHandler._find_column_by_keyword(df, character_keyword)
                if found_idx is not None:
                    actual_character_col_idx = found_idx
                    logging.info(f"根据关键词'{character_keyword}'找到角色列索引: {found_idx}")
                else:
                    logging.warning(f"未找到包含关键词'{character_keyword}'的列，使用默认索引: {character_col_idx}")

            if cv_keyword:
                found_idx = ExcelHandler._find_column_by_keyword(df, cv_keyword)
                if found_idx is not None:
                    actual_cv_col_idx = found_idx
                    logging.info(f"根据关键词'{cv_keyword}'找到CV列索引: {found_idx}")
                else:
                    logging.warning(f"未找到包含关键词'{cv_keyword}'的列，使用默认索引: {cv_col_idx}")

            if price_keyword and price_col_idx is not None:
                found_idx = ExcelHandler._find_column_by_keyword(df, price_keyword)
                if found_idx is not None:
                    actual_price_col_idx = found_idx
                    logging.info(f"根据关键词'{price_keyword}'找到价格列索引: {found_idx}")
                else:
                    logging.warning(f"未找到包含关键词'{price_keyword}'的列，使用默认索引: {price_col_idx}")

            # 构建映射
            character_cv_map = {}
            for _, row in df.iterrows():
                try:
                    character_name = str(row.iloc[actual_character_col_idx]).strip()
                    cv_name = str(row.iloc[actual_cv_col_idx]).strip()

                    if pd.notna(character_name) and pd.notna(cv_name) and cv_name != "nan":
                        # 如果有价格列，则包含价格信息
                        if actual_price_col_idx is not None:
                            try:
                                price = str(row.iloc[actual_price_col_idx]).strip()
                                if pd.notna(price) and price != "nan":
                                    character_cv_map[character_name] = f"{cv_name}|{price}"
                                else:
                                    character_cv_map[character_name] = cv_name
                            except IndexError:
                                logging.warning(f"价格列索引{actual_price_col_idx}超出范围，跳过价格信息")
                                character_cv_map[character_name] = cv_name
                        else:
                            character_cv_map[character_name] = cv_name
                except IndexError as e:
                    logging.error(f"列索引超出范围: 角色列{actual_character_col_idx}, CV列{actual_cv_col_idx}, 错误: {str(e)}")
                    continue

            logging.info(f"成功读取{len(character_cv_map)}个角色-CV映射")
            return character_cv_map
        except Exception as e:
            logging.error(f"读取Excel文件时出错: {str(e)}")
            print(f"读取Excel文件时出错: {str(e)}")
            return {}

    @staticmethod
    def export_to_excel(data: List[Dict[str, Any]], file_path: str,
                       columns: Optional[Dict[str, str]] = None) -> bool:
        """导出数据到Excel

        Args:
            data: 要导出的数据列表
            file_path: 导出文件路径
            columns: 列名映射，键为数据字段名，值为Excel列名

        Returns:
            bool: 是否成功导出
        """
        try:
            df = pd.DataFrame(data)

            # 如果提供了列名映射，则重命名列
            if columns:
                df = df.rename(columns=columns)

            df.to_excel(file_path, index=False)
            return True
        except Exception as e:
            print(f"导出Excel文件时出错: {str(e)}")
            return False

    @staticmethod
    def validate_excel_file(file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """验证Excel文件

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，可选

        Returns:
            Dict[str, Any]: 验证结果，包含是否有效、错误信息、工作表列表等
        """
        result = {
            "valid": False,
            "error": None,
            "sheets": [],
            "columns": [],
            "rows": 0
        }

        try:
            if not os.path.exists(file_path):
                result["error"] = "文件不存在"
                return result

            # 获取所有工作表
            excel_file = pd.ExcelFile(file_path)
            result["sheets"] = excel_file.sheet_names

            # 如果指定了工作表，验证该工作表
            if sheet_name:
                if sheet_name not in result["sheets"]:
                    result["error"] = f"工作表 '{sheet_name}' 不存在"
                    return result

                df = pd.read_excel(file_path, sheet_name=sheet_name)
                result["columns"] = df.columns.tolist()
                result["rows"] = len(df)

            result["valid"] = True
            return result

        except Exception as e:
            result["error"] = f"读取Excel文件时出错: {str(e)}"
            return result
