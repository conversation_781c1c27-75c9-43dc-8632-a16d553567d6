# 角色CV分配工具 - Requirements
# 基于 Augment-Code-Free 架构的依赖配置

# 核心依赖
pywebview>=4.4.0
jinja2>=3.1.0

# 网络功能 - API调用
requests>=2.28.0
urllib3>=1.26.0

# 文件处理 - Excel文件读写
pandas>=2.0.0
openpyxl>=3.1.0

# 开发依赖（可选，用于开发环境）
# 取消注释以下行来安装开发工具
# black>=23.0.0
# isort>=5.12.0
# flake8>=6.0.0
# pytest>=7.0.0
# pytest-cov>=4.0.0

# 额外功能依赖（根据需要取消注释）

# 配置文件支持
# pyyaml>=6.0
# toml>=0.10.0

# 数据库支持
# sqlalchemy>=2.0.0

# 日志和监控
# loguru>=0.7.0

# 加密和安全
# cryptography>=41.0.0

# 图像处理
# pillow>=10.0.0

# 系统集成
# psutil>=5.9.0    # 系统信息
# watchdog>=3.0.0  # 文件监控

# 注意：
# 1. 这个文件包含了最基本的依赖
# 2. 根据您的具体需求添加或移除依赖
# 3. 建议使用虚拟环境来管理依赖
# 4. 推荐使用UV: uv sync (更快更可靠)
# 5. 也可以使用pip: pip install -r requirements.txt
# 6. 开发依赖: uv sync --extra dev 或 pip install -e .[dev]

# UV包管理器使用说明：
# - 安装UV: curl -LsSf https://astral.sh/uv/install.sh | sh
# - 同步依赖: uv sync
# - 添加依赖: uv add package-name
# - 运行应用: uv run python -m modern_gui_app
