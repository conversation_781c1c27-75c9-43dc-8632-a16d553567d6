/**
 * 角色CV分配工具 - 前端JavaScript
 * 
 * 主要功能：
 * - API调用封装
 * - 用户界面交互
 * - 数据管理和状态同步
 * - 文件处理和上传
 */

// 日志管理函数 - 需要在其他函数之前定义
function addLog(message) {
    try {
        const logPanel = document.getElementById('logPanel');
        if (logPanel) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        } else {
            // 如果日志面板还没有加载，输出到控制台
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }

        // 同时输出到终端控制台（延迟调用以避免循环依赖）
        setTimeout(() => {
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.log_to_terminal(message).catch(error => {
                    console.warn('终端日志输出失败:', error);
                });
            }
        }, 0);
    } catch (error) {
        console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    }
}

// 全局状态
let appState = {
    apiToken: '',
    selectedBookId: '',
    books: {},
    characters: [],
    cvs: [],
    characterCvMapping: {},
    cvNicknames: {},
    currentTaskId: '',
    uploadedFilePath: '',
    // 分页状态
    pagination: {
        mapping: {
            currentPage: 1,
            pageSize: 20,
            totalItems: 0,
            totalPages: 0
        },
        cvList: {
            currentPage: 1,
            pageSize: 20,
            totalItems: 0,
            totalPages: 0
        }
    },
    // 存储完整数据用于分页
    fullMappingData: [],
    fullCvData: []
};

// API调用封装
class APIClient {
    static async call(method, ...args) {
        try {
            // 调试信息：记录API调用
            addLog(`🔧 API调用: ${method}(${args.map(arg =>
                typeof arg === 'string' && arg.length > 50 ?
                `${arg.substring(0, 50)}...` :
                JSON.stringify(arg)
            ).join(', ')})`);

            const result = await window.pywebview.api[method](...args);

            // 调试信息：记录API返回结果
            addLog(`📤 API返回: ${method} -> success=${result?.success}, ${
                result?.data ? `data keys=[${Object.keys(result.data).join(', ')}]` : 'no data'
            }${result?.error ? `, error=${result.error}` : ''}`);

            return result;
        } catch (error) {
            console.error(`API调用失败: ${method}`, error);
            addLog(`❌ API异常: ${method} -> ${error.message}`);
            showMessage(`API调用失败: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }
}

async function initializeApp() {
    addLog('🚀 前端应用初始化中...');

    try {
        // 第一步：等待后端系统就绪
        addLog('⏳ 等待后端系统就绪...');
        const backendState = await waitForBackendReady();

        // 第二步：检查API连接状态
        addLog('🔍 检查API连接状态...');
        const status = await APIClient.call('get_status');
        if (status.success) {
            document.getElementById('apiStatus').textContent = '正常';
            addLog('✅ API连接正常');
        } else {
            document.getElementById('apiStatus').textContent = '异常';
            addLog('❌ API连接异常');
            showMessage('API连接异常，请检查服务状态', 'warning');
        }

        // 第三步：分析后端状态并决定初始化策略
        addLog(`📊 后端系统就绪度: ${backendState.ready_percentage}%`);

        const components = backendState.components;
        addLog(`📋 组件状态: API=${components.api_initialized}, Token=${components.api_token_configured}, 配置=${components.config_loaded}, CV映射=${components.cv_nicknames_loaded}`);

        // 根据后端状态决定初始化策略
        if (components.api_token_configured) {
            addLog('🔧 检测到已配置的API Token，开始自动应用配置...');
            await autoApplyBackendConfig();
        } else {
            addLog('ℹ️  未检测到API Token配置，等待用户手动设置');
        }

        // 第四步：确保CV简名映射已加载
        if (!components.cv_nicknames_loaded) {
            addLog('📋 加载CV简名映射...');
            await loadCvNicknames();
        } else {
            addLog('✅ CV简名映射已在后端加载');
        }

        addLog('🎉 前端应用初始化完成');

        // 初始化设置页面TAB（确保默认显示第一个TAB）
        initializeSettingsTabs();

        // 加载Excel目录记忆
        addLog('📁 加载Excel目录记忆...');
        await loadLastExcelDir();

        // 显示最终状态提示
        await showFinalReadyState();

        // 确保欢迎面板是默认显示的标签页（放在最后执行）
        setTimeout(() => {
            switchTabProgrammatically('welcome');
            addLog('🏠 已切换到欢迎首页');
        }, 100);

    } catch (error) {
        addLog(`❌ 应用初始化失败: ${error.message}`);
        showMessage(`应用初始化失败: ${error.message}`, 'error');
        console.error('应用初始化错误:', error);

        // 即使初始化失败，也尝试显示基本状态
        try {
            await showFinalReadyState();
        } catch (finalError) {
            addLog(`❌ 显示最终状态失败: ${finalError.message}`);
        }
    }
}

// 等待后端系统完全就绪
async function waitForBackendReady(maxWaitTime = 10000, checkInterval = 1000) {
    const startTime = Date.now();
    let lastPercentage = 0;

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const readyState = await APIClient.call('get_system_ready_state');
            if (readyState.success) {
                const percentage = readyState.data.ready_percentage;

                // 只在百分比变化时显示日志，避免日志过多
                if (percentage !== lastPercentage) {
                    addLog(`⏳ 后端系统就绪度: ${percentage}%`);
                    lastPercentage = percentage;
                }

                // 如果系统完全就绪，立即返回
                if (readyState.data.is_fully_ready) {
                    addLog('✅ 后端系统完全就绪');
                    return readyState.data;
                }

                // 如果就绪度达到75%以上，认为可以继续（允许部分功能缺失）
                if (percentage >= 75) {
                    addLog(`✅ 后端系统基本就绪 (${percentage}%)，继续初始化`);
                    return readyState.data;
                }
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, checkInterval));

        } catch (error) {
            addLog(`⚠️  检查后端状态失败: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
    }

    // 超时处理 - 不抛出异常，而是返回部分就绪状态
    addLog('⚠️  等待后端就绪超时，使用当前状态继续初始化');
    try {
        const readyState = await APIClient.call('get_system_ready_state');
        if (readyState.success) {
            return readyState.data;
        }
    } catch (error) {
        addLog(`❌ 获取后端状态失败: ${error.message}`);
    }

    // 返回默认状态
    return {
        is_fully_ready: false,
        ready_percentage: 0,
        components: {
            api_initialized: false,
            api_token_configured: false,
            config_loaded: false,
            cv_nicknames_loaded: false
        }
    };
}

// 自动应用后端配置
async function autoApplyBackendConfig() {
    try {
        addLog('🔧 开始自动应用后端配置...');

        // 获取后端配置
        const configResult = await APIClient.call('get_config');
        if (!configResult.success) {
            throw new Error('获取后端配置失败: ' + configResult.error);
        }

        const config = configResult.data;
        addLog('📋 已获取后端配置数据');

        // 自动应用API Token（如果已配置）
        const apiConfig = config.API || {};
        if (apiConfig.default_token) {
            addLog(`🔑 发现配置的API Token: ${apiConfig.default_token.substring(0, 8)}...`);

            // 设置到前端状态
            appState.apiToken = apiConfig.default_token;

            // 确保Token已在后端应用（通常后端初始化时已应用）
            addLog('🔧 确认API Token在后端已应用...');
            const tokenResult = await APIClient.call('set_api_token', apiConfig.default_token);
            if (tokenResult.success) {
                addLog('✅ API Token确认应用成功');

                // 自动加载书籍列表
                addLog('📚 自动加载书籍列表...');
                await loadBooks(false); // 不显示Token警告

            } else {
                addLog(`❌ API Token应用失败: ${tokenResult.error}`);
            }
        } else {
            addLog('⚠️  后端配置中未找到API Token');
        }

        addLog('✅ 后端配置自动应用完成');

    } catch (error) {
        addLog(`❌ 自动应用后端配置失败: ${error.message}`);
        console.error('自动应用配置错误:', error);
    }
}

// 显示最终就绪状态
async function showFinalReadyState() {
    try {
        // 检查最终状态
        const hasToken = !!appState.apiToken;
        const hasBooks = !!(appState.books && Object.keys(appState.books).length > 0);

        if (hasToken && hasBooks) {
            const bookCount = Object.keys(appState.books).length;
            showMessage(`🎉 应用已完全就绪！已加载${bookCount}本书籍，可以开始使用`, 'success');
            addLog(`🎊 系统完全就绪：API Token已配置，${bookCount}本书籍已加载`);
        } else if (hasToken && !hasBooks) {
            showMessage('⚠️  API Token已配置，但书籍列表为空，请检查网络连接', 'warning');
            addLog('⚠️  API Token已配置但书籍列表为空');
        } else {
            showMessage('ℹ️  请在基础设置中配置API Token以开始使用', 'info');
            addLog('ℹ️  等待用户配置API Token');
        }

    } catch (error) {
        addLog(`❌ 检查最终状态失败: ${error.message}`);
    }
}

// 配置管理
async function loadConfig() {
    const config = await APIClient.call('get_config');
    if (config.success) {
        const apiConfig = config.data.API || {};
        if (apiConfig.default_token) {
            document.getElementById('apiToken').value = apiConfig.default_token;
            appState.apiToken = apiConfig.default_token;
            addLog('已加载保存的API Token');
            // 自动加载书籍列表
            await loadBooks();
        }
    }
}

// 加载保存的API Token（简化版本，主要用于向后兼容）
async function loadSavedApiToken() {
    addLog('🔍 检查保存的API Token...');

    try {
        const result = await APIClient.call('get_api_token');
        if (result.success && result.data.has_token) {
            const token = result.data.token;
            addLog(`✅ 发现保存的API Token: ${token.substring(0, 8)}...`);

            // 将Token设置到前端状态
            appState.apiToken = token;
            addLog('✅ API Token已加载到前端状态');

            return token;
        } else {
            addLog('ℹ️  未找到保存的API Token');
            return null;
        }
    } catch (error) {
        addLog(`❌ 检查API Token失败: ${error.message}`);
        return null;
    }
}

// setApiToken函数已移动到设置页面的applyApiToken函数

// 书籍管理
async function loadBooks(showTokenWarning = true) {
    if (!appState.apiToken) {
        if (showTokenWarning) {
            showMessage('请先设置API Token', 'warning');
        }
        addLog('无法加载书籍列表: 未设置API Token');
        return;
    }

    addLog('📚 正在加载书籍列表...');

    try {
        const result = await APIClient.call('get_books', 'unfinished');

        if (result.success) {
            appState.books = result.data.books;
            updateBookSelect();
            const count = result.data.count || 0;
            addLog(`✅ 成功加载${count}本书籍`);

            if (count > 0) {
                showMessage(`成功加载${count}本书籍`, 'success');
            } else {
                showMessage('书籍列表为空', 'info');
            }
        } else {
            const errorMsg = result.error || '未知错误';
            addLog(`❌ 加载书籍失败: ${errorMsg}`);
            showMessage(`加载书籍失败: ${errorMsg}`, 'error');
        }
    } catch (error) {
        addLog(`❌ 加载书籍异常: ${error.message}`);
        showMessage(`加载书籍异常: ${error.message}`, 'error');
        console.error('加载书籍错误:', error);
    }
}

function updateBookSelect() {
    const select = document.getElementById('bookSelect');
    select.innerHTML = '<option value="">请选择书籍</option>';
    
    for (const [bookName, bookId] of Object.entries(appState.books)) {
        const option = document.createElement('option');
        option.value = bookId;
        option.textContent = bookName;
        select.appendChild(option);
    }
}

// 书籍选择变化处理函数
async function onBookSelectionChange() {
    const bookId = document.getElementById('bookSelect').value;
    if (bookId) {
        addLog(`📚 用户选择书籍: ${bookId}`);
        // 自动加载书籍数据，不显示"请先选择书籍"的警告
        await loadBookData(false);
    } else {
        addLog('📚 用户取消书籍选择');
        // 清空相关数据
        appState.characters = {};
        appState.cvs = {};
        appState.selectedBookId = null;

        // 更新相关的选择器
        if (typeof updateCharacterSelect === 'function') {
            updateCharacterSelect();
        }
        if (typeof updateCvSelect === 'function') {
            updateCvSelect();
        }
    }
}

async function loadBookData(showWarningIfNoBook = true) {
    const bookId = document.getElementById('bookSelect').value;
    if (!bookId) {
        if (showWarningIfNoBook) {
            showMessage('请先选择书籍', 'warning');
        }
        return;
    }

    appState.selectedBookId = bookId;
    addLog(`📚 正在加载书籍数据: ${bookId}`);

    try {
        const result = await APIClient.call('get_book_info', bookId);
        if (result.success) {
            appState.characters = result.data.characters;
            appState.cvs = result.data.cvs;
            const characterCount = result.data.character_count || 0;
            const cvCount = result.data.cv_count || 0;

            showMessage(`✅ 成功加载${characterCount}个角色和${cvCount}个CV`, 'success');
            addLog(`✅ 成功加载${characterCount}个角色和${cvCount}个CV`);

            // 自动加载CV列表
            await loadCvList();

            // 更新相关的选择器
            updateCharacterSelect();
            updateCvSelect();
        } else {
            const errorMsg = result.error || '未知错误';
            showMessage(`❌ 加载书籍数据失败: ${errorMsg}`, 'error');
            addLog(`❌ 加载书籍数据失败: ${errorMsg}`);
        }
    } catch (error) {
        addLog(`❌ 加载书籍数据异常: ${error.message}`);
        showMessage(`❌ 加载书籍数据异常: ${error.message}`, 'error');
        console.error('加载书籍数据错误:', error);
    }
}

// 旧的文件处理函数已移动到角色-CV映射页面

// updateMappingTable函数已移动到角色-CV映射页面的displayMappingData函数

// CV列表管理
async function loadCvList() {
    if (!appState.selectedBookId) {
        showMessage('请先选择书籍', 'warning');
        return;
    }

    addLog('正在加载CV列表...');

    const result = await APIClient.call('get_cvs', appState.selectedBookId);

    if (result.success) {
        appState.cvs = result.data.cvs;
        updateCvListTable();
        showMessage(`成功加载${result.data.count}个CV`, 'success');
        addLog(`成功加载${result.data.count}个CV`);
    } else {
        showMessage('加载CV列表失败: ' + result.error, 'error');
        addLog('加载CV列表失败: ' + result.error);
    }
}

function updateCvListTable() {
    // 存储完整数据用于分页
    appState.fullCvData = appState.cvs || [];

    // 使用分页显示
    updateCvListTableWithPagination(appState.fullCvData);
}

function updateCvListTableWithPagination(cvData) {
    const tableContainer = document.getElementById('cvListTableContainer');
    const paginationContainer = document.getElementById('cvListPaginationContainer');

    if (!cvData || cvData.length === 0) {
        tableContainer.innerHTML = '<p>暂无CV数据</p>';
        paginationContainer.innerHTML = '';
        return;
    }

    // 如果数据量小于等于20条，直接显示不分页
    if (cvData.length <= 20) {
        const html = renderCvListTable(cvData);
        tableContainer.innerHTML = html;
        paginationContainer.innerHTML = '';
        return;
    }

    // 确保分页管理器已初始化
    initializePaginationManagers();

    // 使用分页管理器渲染
    cvListPagination.renderWithPagination(cvData);
}

function renderCvListTable(pageData, paginationState = null) {
    let html = `
        <table class="mapping-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>CV名称</th>
                    <th>CV ID</th>
                    <th>性别</th>
                    <th>单价（元）</th>
                    <th>合作关系</th>
                </tr>
            </thead>
            <tbody>
    `;

    pageData.forEach((cv, index) => {
        const name = cv.name || '未知';
        const id = cv.id || '未知';
        const gender = cv.gender || '未知';
        const price = cv.price ? `${cv.price}` : '未设置';
        const cooperation = cv.cooperation || cv.status || '未知';

        // 计算全局索引（考虑分页）
        const globalIndex = paginationState ?
            (paginationState.currentPage - 1) * paginationState.pageSize + index :
            index;

        html += `
            <tr>
                <td>${globalIndex + 1}</td>
                <td>${name}</td>
                <td>${id}</td>
                <td>${gender}</td>
                <td>${price}</td>
                <td>${cooperation}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    return html;
}

// CV简名管理
async function loadCvNicknames() {
    const result = await APIClient.call('get_cv_nicknames');
    if (result.success) {
        appState.cvNicknames = result.data.cv_nicknames;
        updateNicknamesTable();
        addLog(`加载${result.data.count}个CV简名映射`);
    } else {
        addLog('加载CV简名映射失败: ' + result.error);
    }
}

async function addCvNickname() {
    const nickname = document.getElementById('cvNickname').value.trim();
    const fullname = document.getElementById('cvFullname').value.trim();
    
    if (!nickname || !fullname) {
        showMessage('请输入简名和全名', 'warning');
        return;
    }
    
    const result = await APIClient.call('update_cv_nickname_mapping', nickname, fullname);
    if (result.success) {
        document.getElementById('cvNickname').value = '';
        document.getElementById('cvFullname').value = '';
        await loadCvNicknames();
        showMessage('CV简名映射添加成功', 'success');
        addLog(`添加CV简名映射: ${nickname} -> ${fullname}`);
    } else {
        showMessage('添加失败: ' + result.error, 'error');
    }
}

function updateNicknamesTable() {
    const container = document.getElementById('nicknamesTableContainer');
    
    if (Object.keys(appState.cvNicknames).length === 0) {
        container.innerHTML = '<p>暂无简名映射</p>';
        return;
    }
    
    let html = `
        <table class="mapping-table">
            <thead>
                <tr>
                    <th>简名</th>
                    <th>全名</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    for (const [nickname, fullname] of Object.entries(appState.cvNicknames)) {
        html += `
            <tr>
                <td>${nickname}</td>
                <td>${fullname}</td>
                <td>
                    <button class="btn btn-warning" onclick="deleteCvNickname('${nickname}')">删除</button>
                </td>
            </tr>
        `;
    }
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

async function deleteCvNickname(nickname) {
    if (!confirm(`确定要删除简名映射 "${nickname}" 吗？`)) {
        return;
    }

    const result = await APIClient.call('delete_cv_nickname_mapping', nickname);
    if (result.success) {
        await loadCvNicknames();
        showMessage('删除成功', 'success');
        addLog(`删除CV简名映射: ${nickname}`);
    } else {
        showMessage('删除失败: ' + result.error, 'error');
    }
}

// 分配任务管理
async function startAssignment() {
    if (!appState.selectedBookId) {
        showMessage('请先选择书籍并加载数据', 'warning');
        return;
    }

    if (Object.keys(appState.characterCvMapping).length === 0) {
        showMessage('请先上传并解析Excel文件', 'warning');
        return;
    }

    const enableReal = document.getElementById('enableRealAssignment').checked;
    const mode = enableReal ? '正式模式' : '测试模式';

    if (enableReal && !confirm('确定要启用真实分配模式吗？这将实际修改系统数据！')) {
        return;
    }

    addLog(`开始分配任务 (${mode})`);

    // 准备映射数据（只传递CV名称）
    const mapping = {};
    for (const [character, cvInfo] of Object.entries(appState.characterCvMapping)) {
        mapping[character] = cvInfo.cv;
    }

    const result = await APIClient.call('start_assignment', appState.selectedBookId, mapping, enableReal);
    if (result.success) {
        appState.currentTaskId = result.data.task_id;
        document.getElementById('progressContainer').classList.remove('hidden');
        showMessage(`分配任务已启动 (${mode})`, 'success');
        addLog(`任务ID: ${appState.currentTaskId}`);

        // 开始监控进度
        monitorAssignmentProgress();
    } else {
        showMessage('启动分配任务失败: ' + result.error, 'error');
        addLog('启动分配任务失败: ' + result.error);
    }
}

async function monitorAssignmentProgress() {
    if (!appState.currentTaskId) return;

    const result = await APIClient.call('get_assignment_progress', appState.currentTaskId);
    if (result.success) {
        const data = result.data;

        // 更新进度条
        document.getElementById('progressFill').style.width = data.progress + '%';
        document.getElementById('progressText').textContent = data.current_step || `进度: ${data.progress}%`;

        // 如果任务完成
        if (data.status === 'completed') {
            showAssignmentResults(data.results);
            showMessage('分配任务完成', 'success');
            addLog('分配任务完成');
            return;
        }

        // 如果任务失败
        if (data.status === 'failed') {
            showMessage('分配任务失败: ' + data.error_message, 'error');
            addLog('分配任务失败: ' + data.error_message);
            return;
        }

        // 继续监控
        if (data.status === 'running') {
            setTimeout(monitorAssignmentProgress, 1000);
        }
    }
}

function showAssignmentResults(results) {
    const container = document.getElementById('assignmentResults');
    container.classList.remove('hidden');

    let html = `
        <div class="alert alert-success">
            <h4>分配统计</h4>
            <p>成功: ${results.success} | 失败: ${results.failed} | 未匹配: ${results.unassigned}</p>
        </div>
    `;

    if (results.unmatched_characters && results.unmatched_characters.length > 0) {
        html += `
            <div class="alert alert-warning">
                <h4>未匹配的角色 (${results.unmatched_characters.length}个)</h4>
                <p>${results.unmatched_characters.join(', ')}</p>
            </div>
        `;
    }

    if (results.unmatched_cvs && results.unmatched_cvs.length > 0) {
        html += `
            <div class="alert alert-warning">
                <h4>未匹配的CV (${results.unmatched_cvs.length}个)</h4>
                <p>${results.unmatched_cvs.join(', ')}</p>
            </div>
        `;
    }

    if (results.failed_assignments && results.failed_assignments.length > 0) {
        html += `
            <div class="alert alert-error">
                <h4>分配失败的项目</h4>
                <ul>
                    ${results.failed_assignments.map(item => `<li>${item}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    document.getElementById('resultsContent').innerHTML = html;
}

async function stopAssignment() {
    // 这里可以添加停止任务的逻辑
    appState.currentTaskId = '';
    document.getElementById('progressContainer').classList.add('hidden');
    document.getElementById('assignmentResults').classList.add('hidden');
    showMessage('任务已停止', 'warning');
    addLog('任务已停止');
}

// 标签页管理
function switchTabProgrammatically(tabName) {
    // 程序化切换TAB，不依赖event对象
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // 移除所有导航链接的active类
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 显示选中的标签页
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // 查找并激活对应的导航链接
    const targetNavLink = document.querySelector(`[data-tab="${tabName}"]`);
    if (targetNavLink) {
        targetNavLink.classList.add('active');
    }

    // 特殊处理：当切换到基础设置页面时，确保配置数据已加载
    if (tabName === 'settings') {
        // 延迟一小段时间确保DOM元素完全可见后再加载配置
        setTimeout(() => {
            loadSettings();
        }, 100);
    }

    // 特殊处理：当切换到欢迎页面时，更新状态显示
    if (tabName === 'welcome') {
        setTimeout(() => {
            updateWelcomeStatus();
        }, 100);
    }
}

function switchTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // 移除所有标签的active类
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 显示选中的标签页
    document.getElementById(tabName + 'Tab').classList.add('active');

    // 添加active类到选中的标签
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // 如果没有event对象，手动查找并激活对应的标签
        document.querySelectorAll('.tab').forEach(tab => {
            if (tab.onclick && tab.onclick.toString().includes(`'${tabName}'`)) {
                tab.classList.add('active');
            }
        });
    }

    // 特殊处理：当切换到基础设置页面时，确保配置数据已加载
    if (tabName === 'settings') {
        // 延迟一小段时间确保DOM元素完全可见后再加载配置
        setTimeout(() => {
            loadSettings();
            // 确保默认显示第一个设置TAB
            switchSettingsTab('api');
        }, 100);
    }

    // 特殊处理：当切换到欢迎页面时，更新状态显示
    if (tabName === 'welcome') {
        setTimeout(() => {
            updateWelcomeStatus();
        }, 100);
    }
}

// 设置页面内部TAB切换功能
function switchSettingsTab(tabName) {
    // 移除所有设置TAB的active类
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 隐藏所有设置TAB内容
    document.querySelectorAll('.settings-tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // 激活选中的设置TAB
    const selectedTab = document.querySelector(`.settings-tab[data-tab="${tabName}"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // 显示对应的设置TAB内容
    const selectedPane = document.getElementById(`${tabName}SettingsTab`);
    if (selectedPane) {
        selectedPane.classList.add('active');
    }

    // 记录TAB切换日志
    addLog(`切换到设置TAB: ${getSettingsTabName(tabName)}`);
}

// 获取设置TAB的中文名称
function getSettingsTabName(tabName) {
    const tabNames = {
        'api': 'API设置',
        'excel': 'EXCEL设置',
        'files': '文件路径设置'
    };
    return tabNames[tabName] || tabName;
}

// 初始化设置页面TAB
function initializeSettingsTabs() {
    // 确保默认显示第一个TAB（API设置）
    switchSettingsTab('api');
    addLog('🔧 设置页面TAB初始化完成，默认显示API设置');
}

// 日志管理函数已在文件开头定义，这里只保留终端输出功能
function logToTerminal(message) {
    // 输出到终端控制台
    APIClient.call('log_to_terminal', message).catch(error => {
        console.warn('终端日志输出失败:', error);
    });
}

function clearLogs() {
    document.getElementById('logPanel').innerHTML = '';
    addLog('日志已清空');
}

// 消息提示
function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type}`;
    messageDiv.textContent = message;

    container.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 设置管理
async function loadSettings() {
    addLog('正在加载配置...');

    try {
        // 检查基础设置TAB是否可见
        const settingsTab = document.getElementById('settingsTab');
        if (!settingsTab) {
            addLog('❌ 基础设置TAB页面不存在');
            showMessage('界面错误: 找不到基础设置页面', 'error');
            return;
        }

        const isTabVisible = settingsTab.classList.contains('active');
        addLog(`基础设置TAB可见状态: ${isTabVisible}`);

        const result = await APIClient.call('get_config');
        addLog(`get_config API调用结果: success=${result.success}`);

        if (result.success) {
            const config = result.data;
            addLog(`配置数据结构: ${JSON.stringify(config, null, 2)}`);

            // 检查HTML元素是否存在
            const elements = {
                'settingsApiToken': document.getElementById('settingsApiToken'),
                'settingsApiBaseUrl': document.getElementById('settingsApiBaseUrl'),
                'settingsEnableRealAssignment': document.getElementById('settingsEnableRealAssignment'),
                'settingsCharacterKeyword': document.getElementById('settingsCharacterKeyword'),
                'settingsCvKeyword': document.getElementById('settingsCvKeyword'),
                'settingsPriceKeyword': document.getElementById('settingsPriceKeyword'),
                'settingsHeaderRow': document.getElementById('settingsHeaderRow'),
                'settingsCvConfigFile': document.getElementById('settingsCvConfigFile'),
                'settingsLastExcelDir': document.getElementById('settingsLastExcelDir')
            };

            // 检查缺失的元素
            const missingElements = [];
            const hiddenElements = [];
            for (const [id, element] of Object.entries(elements)) {
                if (!element) {
                    missingElements.push(id);
                } else {
                    // 检查元素是否可见
                    const isVisible = element.offsetParent !== null;
                    if (!isVisible) {
                        hiddenElements.push(id);
                    }
                }
            }

            if (missingElements.length > 0) {
                addLog(`❌ 找不到以下HTML元素: ${missingElements.join(', ')}`);
                showMessage(`界面错误: 找不到设置表单元素`, 'error');
                return;
            }

            if (hiddenElements.length > 0) {
                addLog(`⚠️ 以下HTML元素当前不可见: ${hiddenElements.join(', ')}`);
                addLog('这可能是因为基础设置TAB页面当前未激活');
            }

            addLog('✅ 所有HTML元素都找到了，开始填充数据...');

            // API配置
            const apiToken = config.API?.default_token || '';
            const apiBaseUrl = config.API?.base_url || '';
            const enableRealAssignment = config.API?.enable_real_assignment === 'true';

            elements.settingsApiToken.value = apiToken;
            elements.settingsApiBaseUrl.value = apiBaseUrl;
            elements.settingsEnableRealAssignment.checked = enableRealAssignment;

            addLog(`API配置填充: Token=${apiToken ? apiToken.substring(0, 8) + '...' : '(空)'}, BaseURL=${apiBaseUrl}, RealAssignment=${enableRealAssignment}`);

            // Excel配置
            const characterKeyword = config.EXCEL?.character_column_keyword || '';
            const cvKeyword = config.EXCEL?.cv_column_keyword || '';
            const priceKeyword = config.EXCEL?.price_column_keyword || '';
            const headerRow = config.EXCEL?.header_row || '';

            elements.settingsCharacterKeyword.value = characterKeyword;
            elements.settingsCvKeyword.value = cvKeyword;
            elements.settingsPriceKeyword.value = priceKeyword;
            elements.settingsHeaderRow.value = headerRow;

            addLog(`Excel配置填充: 角色关键词=${characterKeyword}, CV关键词=${cvKeyword}, 价格关键词=${priceKeyword}, 表头行=${headerRow}`);

            // 文件路径配置
            const cvConfigFile = config.FILES?.cv_config_file || '';
            const lastExcelDir = config.FILES?.last_excel_directory || '';

            elements.settingsCvConfigFile.value = cvConfigFile;
            elements.settingsLastExcelDir.value = lastExcelDir;

            addLog(`文件路径配置填充: CV配置文件=${cvConfigFile}, 上次Excel目录=${lastExcelDir}`);

            addLog('✅ 配置加载完成');
            showMessage('配置加载成功', 'success');
        } else {
            addLog(`❌ 配置加载失败: ${result.error}`);
            showMessage('配置加载失败: ' + result.error, 'error');
        }
    } catch (error) {
        addLog(`❌ 配置加载异常: ${error.message}`);
        console.error('loadSettings error:', error);
        showMessage('配置加载异常: ' + error.message, 'error');
    }
}

async function saveSettings() {
    addLog('正在保存配置...');

    const newConfig = {
        API: {
            default_token: document.getElementById('settingsApiToken').value.trim(),
            base_url: document.getElementById('settingsApiBaseUrl').value.trim(),
            enable_real_assignment: document.getElementById('settingsEnableRealAssignment').checked ? 'true' : 'false'
        },
        EXCEL: {
            character_column_keyword: document.getElementById('settingsCharacterKeyword').value.trim(),
            cv_column_keyword: document.getElementById('settingsCvKeyword').value.trim(),
            price_column_keyword: document.getElementById('settingsPriceKeyword').value.trim(),
            header_row: document.getElementById('settingsHeaderRow').value.trim()
        },
        FILES: {
            cv_config_file: document.getElementById('settingsCvConfigFile').value.trim(),
            last_excel_directory: document.getElementById('settingsLastExcelDir').value.trim()
        }
    };

    const result = await APIClient.call('update_config', newConfig);
    if (result.success) {
        addLog('配置保存成功');
        showMessage('配置保存成功', 'success');

        // 如果API Token有变化，自动应用
        const currentToken = appState.apiToken;
        const newToken = newConfig.API.default_token;
        if (newToken && newToken !== currentToken) {
            await applyApiToken();
        }
    } else {
        addLog('配置保存失败: ' + result.error);
        showMessage('配置保存失败: ' + result.error, 'error');
    }
}

async function resetSettings() {
    if (!confirm('确定要重置所有配置为默认值吗？这将清除所有自定义设置！')) {
        return;
    }

    addLog('正在重置配置...');

    const result = await APIClient.call('reset_config');
    if (result.success) {
        addLog('配置重置成功');
        showMessage('配置已重置为默认值', 'success');
        await loadSettings(); // 重新加载显示
    } else {
        addLog('配置重置失败: ' + result.error);
        showMessage('配置重置失败: ' + result.error, 'error');
    }
}

async function applyApiToken() {
    const token = document.getElementById('settingsApiToken').value.trim();
    if (!token) {
        showMessage('请输入API Token', 'warning');
        return;
    }

    const result = await APIClient.call('set_api_token', token);
    if (result.success) {
        appState.apiToken = token;
        showMessage('API Token应用成功', 'success');
        addLog('✅ API Token应用成功');
        await loadBooks(false); // 自动加载书籍列表，不显示Token警告
    } else {
        addLog(`❌ API Token应用失败: ${result.error}`);
        showMessage('API Token应用失败: ' + result.error, 'error');
    }
}

async function loadLastExcelDir() {
    try {
        const result = await APIClient.call('get_last_excel_directory');
        if (result.success && result.data) {
            const { directory, exists } = result.data;
            const dirInput = document.getElementById('settingsLastExcelDir');
            if (dirInput) {
                dirInput.value = directory || '';
                // 如果目录不存在，添加视觉提示
                if (directory && !exists) {
                    dirInput.classList.add('input-error');
                    dirInput.title = '此路径不存在';
                } else {
                    dirInput.classList.remove('input-error');
                    dirInput.title = '';
                }
            }
            addLog(`📁 加载Excel目录记忆: ${directory || '(无)'}`);
        }
    } catch (error) {
        addLog(`❌ 加载Excel目录记忆失败: ${error.message}`);
    }
}

async function clearLastExcelDir() {
    try {
        const result = await APIClient.call('clear_last_excel_directory');
        if (result.success) {
            document.getElementById('settingsLastExcelDir').value = '';
            showMessage('已清空Excel文件目录记忆', 'success');
            addLog('✅ 成功清空Excel文件目录记忆');
        } else {
            showMessage('清空目录记忆失败: ' + result.error, 'error');
            addLog(`❌ 清空目录记忆失败: ${result.error}`);
        }
    } catch (error) {
        showMessage('清空目录记忆时发生错误: ' + error.message, 'error');
        addLog(`❌ 清空目录记忆异常: ${error.message}`);
    }
}

// 角色-CV映射页面的Excel文件处理
let mappingFilePath = null;

async function selectExcelFileDialog() {
    addLog('📂 [映射] 打开文件选择对话框...');

    try {
        const result = await APIClient.call('select_excel_file_dialog');

        addLog(`📂 [映射] 文件选择结果: success=${result?.success}`);

        if (result.success && result.data) {
            const { file_path, filename, size, directory } = result.data;

            addLog(`✅ [映射] 成功选择文件: ${filename}`);
            addLog(`📁 [映射] 文件路径: ${file_path}`);
            addLog(`📏 [映射] 文件大小: ${size} bytes`);
            if (directory) {
                addLog(`💾 [映射] 已记忆目录: ${directory}`);
            }

            // 更新UI显示
            document.getElementById('mappingFileName').textContent = filename;
            document.getElementById('mappingFilePath').textContent = file_path;
            document.getElementById('mappingFileInfo').classList.remove('hidden');

            // 保存文件路径
            mappingFilePath = file_path;

            showMessage(`文件选择成功: ${filename}`, 'success');

            // 自动触发解析
            addLog('🚀 [映射] 自动开始解析Excel文件...');
            await parseMappingExcelFile();

        } else {
            const errorMsg = result.error || '未知错误';
            addLog(`❌ [映射] 文件选择失败: ${errorMsg}`);

            // 如果是用户取消，不显示错误消息
            if (errorMsg !== '用户取消了文件选择') {
                showMessage('文件选择失败: ' + errorMsg, 'error');
            }
        }

    } catch (error) {
        addLog(`❌ [映射] 文件选择异常: ${error.message}`);
        showMessage('文件选择失败: ' + error.message, 'error');
    }
}

// 移除原来的文件选择处理函数，现在使用pywebview文件对话框

// 移除Base64转换函数，不再需要

// 移除配置加载函数，后端直接从配置文件获取

async function parseMappingExcelFile() {
    addLog('🔧 [映射] 开始解析Excel文件...');

    if (!mappingFilePath) {
        addLog('🔧 [映射] 错误: 未设置文件路径');
        showMessage('请先选择Excel文件', 'warning');
        return;
    }

    addLog(`🔧 [映射] 文件路径: ${mappingFilePath}`);
    document.getElementById('mappingStatus').textContent = '解析中...';

    try {
        // 直接调用简化的API，后端从配置文件获取所有配置
        const result = await APIClient.call('parse_excel_mapping_from_path', mappingFilePath);

        if (result.success) {
            displayMappingData(result.data);
            addLog(`成功解析${result.data.length}条角色-CV映射`);
            document.getElementById('mappingStatus').textContent = `已解析${result.data.length}条映射`;

            // 切换按钮显示状态：隐藏选择文件按钮，显示操作按钮组
            switchToActionButtons();
        } else {
            showMessage('解析Excel文件失败: ' + result.error, 'error');
            addLog('解析Excel文件失败: ' + result.error);
            document.getElementById('mappingStatus').textContent = '解析失败';
        }
    } catch (error) {
        showMessage('解析Excel文件时发生错误: ' + error.message, 'error');
        addLog('解析Excel文件时发生错误: ' + error.message);
        document.getElementById('mappingStatus').textContent = '解析错误';
    }
}

function displayMappingData(mappingData) {
    // 存储完整数据用于分页
    appState.fullMappingData = mappingData || [];

    // 使用分页显示
    displayMappingDataWithPagination(appState.fullMappingData);
}

function displayMappingDataWithPagination(mappingData) {
    const tableContainer = document.getElementById('mappingTableContainer');
    const paginationContainer = document.getElementById('mappingPaginationContainer');

    if (!mappingData || mappingData.length === 0) {
        tableContainer.innerHTML = '<p>没有找到有效的角色-CV映射数据</p>';
        paginationContainer.innerHTML = '';
        return;
    }

    // 如果数据量小于等于20条，直接显示不分页
    if (mappingData.length <= 20) {
        const html = renderMappingTable(mappingData);
        tableContainer.innerHTML = html;
        paginationContainer.innerHTML = '';
        return;
    }

    // 确保分页管理器已初始化
    initializePaginationManagers();

    // 使用分页管理器渲染
    mappingPagination.renderWithPagination(mappingData);
}

function renderMappingTable(pageData, paginationState = null) {
    // 创建表格
    let html = `
        <table class="mapping-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>角色名称</th>
                    <th>CV名称</th>
                    <th>价格</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;

    pageData.forEach((item, index) => {
        const parts = item.cv_info ? item.cv_info.split('|') : ['', ''];
        const cvName = parts[0] || '';
        const price = parts[1] || '';

        // 计算全局索引（考虑分页）
        const globalIndex = paginationState ?
            (paginationState.currentPage - 1) * paginationState.pageSize + index :
            index;

        html += `
            <tr>
                <td>${globalIndex + 1}</td>
                <td>${item.character_name || ''}</td>
                <td>${cvName}</td>
                <td>${price}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editMappingRow(${globalIndex})">编辑</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteMappingRow(${globalIndex})">删除</button>
                </td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
    `;

    return html;
}

async function reprocessMappingFile() {
    await parseMappingExcelFile();
}

// 移除预览函数，后端直接处理所有配置

// 按钮状态切换函数
function switchToActionButtons() {
    addLog('🔄 [映射] 切换到操作按钮状态');

    // 隐藏文件选择按钮
    document.getElementById('selectFileBtn').classList.add('hidden');

    // 显示操作按钮组
    document.getElementById('actionButtonsGroup').classList.remove('hidden');

    addLog('✅ [映射] 按钮状态切换完成');
}

function switchToSelectButton() {
    addLog('🔄 [映射] 切换到文件选择按钮状态');

    // 显示文件选择按钮
    document.getElementById('selectFileBtn').classList.remove('hidden');

    // 隐藏操作按钮组
    document.getElementById('actionButtonsGroup').classList.add('hidden');

    addLog('✅ [映射] 按钮状态重置完成');
}

function clearMappingFile() {
    // 清除文件信息显示
    document.getElementById('mappingFileInfo').classList.add('hidden');
    document.getElementById('mappingStatus').textContent = '';

    // 重置按钮状态到初始状态
    switchToSelectButton();

    // 清除文件路径
    mappingFilePath = null;

    addLog('🗑️ [映射] 已清除Excel文件，恢复初始状态');
}

function clearMappingData() {
    addLog('🗑️ [映射] 开始清空映射数据...');

    // 清空映射数据表格
    document.getElementById('mappingTableContainer').innerHTML = '<p>请先选择Excel文件进行解析</p>';

    // 清除文件信息和状态，并重置按钮状态
    clearMappingFile();

    addLog('✅ [映射] 映射数据已清空，界面已重置');
    showMessage('映射数据已清空', 'success');
}

async function exportMappingData() {
    // TODO: 实现导出功能
    showMessage('导出功能开发中...', 'info');
}

function editMappingRow(index) {
    // TODO: 实现编辑功能
    showMessage('编辑功能开发中...', 'info');
}

function deleteMappingRow(index) {
    // TODO: 实现删除功能
    showMessage('删除功能开发中...', 'info');
}

// 应用初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();
});

// ==================== 分页功能实现 ====================

// 分页管理类
class PaginationManager {
    constructor(containerId, dataKey, renderFunction, paginationContainerId = null) {
        this.containerId = containerId;
        this.dataKey = dataKey;
        this.renderFunction = renderFunction;
        this.paginationContainerId = paginationContainerId || containerId;
        this.paginationState = appState.pagination[dataKey];
    }

    // 计算分页信息
    calculatePagination(totalItems) {
        this.paginationState.totalItems = totalItems;
        this.paginationState.totalPages = Math.ceil(totalItems / this.paginationState.pageSize);

        // 确保当前页在有效范围内
        if (this.paginationState.currentPage > this.paginationState.totalPages) {
            this.paginationState.currentPage = Math.max(1, this.paginationState.totalPages);
        }
    }

    // 获取当前页的数据
    getCurrentPageData(allData) {
        const startIndex = (this.paginationState.currentPage - 1) * this.paginationState.pageSize;
        const endIndex = startIndex + this.paginationState.pageSize;
        return allData.slice(startIndex, endIndex);
    }

    // 渲染分页控件
    renderPaginationControls() {
        const { currentPage, totalPages, totalItems, pageSize } = this.paginationState;

        if (totalItems <= pageSize) {
            return ''; // 不需要分页
        }

        const startItem = (currentPage - 1) * pageSize + 1;
        const endItem = Math.min(currentPage * pageSize, totalItems);

        return `
            <div class="pagination-container">
                <div class="pagination-info">
                    <span>显示 ${startItem}-${endItem} 条，共 ${totalItems} 条记录</span>
                    <select class="pagination-size-select" onchange="changePaginationSize('${this.dataKey}', this.value)">
                        <option value="10" ${pageSize === 10 ? 'selected' : ''}>10条/页</option>
                        <option value="20" ${pageSize === 20 ? 'selected' : ''}>20条/页</option>
                        <option value="50" ${pageSize === 50 ? 'selected' : ''}>50条/页</option>
                        <option value="100" ${pageSize === 100 ? 'selected' : ''}>100条/页</option>
                    </select>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm btn-secondary"
                            onclick="changePage('${this.dataKey}', ${currentPage - 1})"
                            ${currentPage <= 1 ? 'disabled' : ''}>
                        上一页
                    </button>
                    <div class="pagination-pages">
                        ${this.generatePageNumbers()}
                    </div>
                    <button class="btn btn-sm btn-secondary"
                            onclick="changePage('${this.dataKey}', ${currentPage + 1})"
                            ${currentPage >= totalPages ? 'disabled' : ''}>
                        下一页
                    </button>
                </div>
                <div class="pagination-jump">
                    <span>跳转到</span>
                    <input type="number" class="pagination-jump-input"
                           min="1" max="${totalPages}" value="${currentPage}"
                           onchange="jumpToPage('${this.dataKey}', this.value)">
                    <span>页</span>
                </div>
            </div>
        `;
    }

    // 生成页码按钮
    generatePageNumbers() {
        const { currentPage, totalPages } = this.paginationState;
        let pages = [];

        // 显示逻辑：当前页前后各显示2页
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        // 如果总页数较少，显示所有页码
        if (totalPages <= 7) {
            startPage = 1;
            endPage = totalPages;
        }

        // 添加第一页
        if (startPage > 1) {
            pages.push(`<button class="btn btn-sm btn-outline-primary pagination-page" onclick="changePage('${this.dataKey}', 1)">1</button>`);
            if (startPage > 2) {
                pages.push('<span class="pagination-ellipsis">...</span>');
            }
        }

        // 添加中间页码
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            pages.push(`<button class="btn btn-sm ${isActive ? 'btn-primary' : 'btn-outline-primary'} pagination-page"
                        onclick="changePage('${this.dataKey}', ${i})">${i}</button>`);
        }

        // 添加最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pages.push('<span class="pagination-ellipsis">...</span>');
            }
            pages.push(`<button class="btn btn-sm btn-outline-primary pagination-page" onclick="changePage('${this.dataKey}', ${totalPages})">${totalPages}</button>`);
        }

        return pages.join('');
    }

    // 渲染带分页的数据
    renderWithPagination(allData) {
        this.calculatePagination(allData.length);
        const pageData = this.getCurrentPageData(allData);
        const tableHtml = this.renderFunction(pageData, this.paginationState);
        const paginationHtml = this.renderPaginationControls();

        const tableContainer = document.getElementById(this.containerId);
        const paginationContainer = document.getElementById(this.paginationContainerId);

        // 如果有独立的分页容器，分别渲染
        if (paginationContainer && paginationContainer !== tableContainer) {
            tableContainer.innerHTML = tableHtml;
            paginationContainer.innerHTML = paginationHtml;
        } else {
            // 传统方式：表格和分页在同一容器
            tableContainer.innerHTML = tableHtml + paginationHtml;
        }
    }
}

// 分页控制函数
function changePage(dataKey, newPage) {
    const paginationState = appState.pagination[dataKey];
    if (newPage >= 1 && newPage <= paginationState.totalPages) {
        paginationState.currentPage = newPage;
        refreshPaginatedData(dataKey);
    }
}

function changePaginationSize(dataKey, newSize) {
    const paginationState = appState.pagination[dataKey];
    paginationState.pageSize = parseInt(newSize);
    paginationState.currentPage = 1; // 重置到第一页
    refreshPaginatedData(dataKey);
}

function jumpToPage(dataKey, pageNumber) {
    const page = parseInt(pageNumber);
    const paginationState = appState.pagination[dataKey];
    if (page >= 1 && page <= paginationState.totalPages) {
        paginationState.currentPage = page;
        refreshPaginatedData(dataKey);
    }
}

function refreshPaginatedData(dataKey) {
    if (dataKey === 'mapping') {
        displayMappingDataWithPagination(appState.fullMappingData);
    } else if (dataKey === 'cvList') {
        updateCvListTableWithPagination(appState.fullCvData);
    }
}

// 分页管理器实例（延迟初始化）
let mappingPagination = null;
let cvListPagination = null;

// 初始化分页管理器
function initializePaginationManagers() {
    if (!mappingPagination) {
        mappingPagination = new PaginationManager('mappingTableContainer', 'mapping', renderMappingTable, 'mappingPaginationContainer');
    }
    if (!cvListPagination) {
        cvListPagination = new PaginationManager('cvListTableContainer', 'cvList', renderCvListTable, 'cvListPaginationContainer');
    }
}

// ==================== 欢迎面板状态管理 ====================

/**
 * 更新欢迎面板的状态显示
 */
function updateWelcomeStatus() {
    updateWelcomeApiStatus();
    updateWelcomeBookStatus();
    updateWelcomeFileStatus();
    updateWelcomeTaskStatus();
}

/**
 * 更新API连接状态
 */
function updateWelcomeApiStatus() {
    const apiStatusElement = document.getElementById('welcomeApiStatus');
    const apiStatusIcon = document.getElementById('welcomeApiStatusIcon');
    const headerApiStatus = document.getElementById('apiStatus');

    if (!apiStatusElement || !apiStatusIcon) return;

    // 从头部状态获取当前API状态
    const currentStatus = headerApiStatus ? headerApiStatus.textContent : '检查中...';

    apiStatusElement.textContent = currentStatus;

    // 更新图标和样式
    if (currentStatus === '正常') {
        apiStatusIcon.className = 'status-icon success';
        apiStatusIcon.textContent = '✅';
    } else if (currentStatus === '异常') {
        apiStatusIcon.className = 'status-icon error';
        apiStatusIcon.textContent = '❌';
    } else {
        apiStatusIcon.className = 'status-icon info';
        apiStatusIcon.textContent = '🔗';
    }
}

/**
 * 更新当前书籍状态
 */
function updateWelcomeBookStatus() {
    const bookStatusElement = document.getElementById('welcomeBookStatus');
    const bookStatusIcon = document.getElementById('welcomeBookStatusIcon');
    const bookSelect = document.getElementById('bookSelect');

    if (!bookStatusElement || !bookStatusIcon) return;

    if (bookSelect && bookSelect.value) {
        const selectedOption = bookSelect.options[bookSelect.selectedIndex];
        const bookName = selectedOption ? selectedOption.text : '未知书籍';
        bookStatusElement.textContent = bookName;
        bookStatusIcon.className = 'status-icon success';
        bookStatusIcon.textContent = '📚';
    } else {
        bookStatusElement.textContent = '未选择';
        bookStatusIcon.className = 'status-icon warning';
        bookStatusIcon.textContent = '⚠️';
    }
}

/**
 * 更新Excel文件状态
 */
function updateWelcomeFileStatus() {
    const fileStatusElement = document.getElementById('welcomeFileStatus');
    const fileStatusIcon = document.getElementById('welcomeFileStatusIcon');

    if (!fileStatusElement || !fileStatusIcon) return;

    // 检查是否有已上传的文件
    const mappingFileInfo = document.getElementById('mappingFileInfo');
    const mappingFileName = document.getElementById('mappingFileName');

    if (mappingFileInfo && !mappingFileInfo.classList.contains('hidden') && mappingFileName) {
        const fileName = mappingFileName.textContent;
        fileStatusElement.textContent = fileName || '已上传';
        fileStatusIcon.className = 'status-icon success';
        fileStatusIcon.textContent = '📄';
    } else {
        fileStatusElement.textContent = '未上传';
        fileStatusIcon.className = 'status-icon warning';
        fileStatusIcon.textContent = '⚠️';
    }
}

/**
 * 更新任务状态
 */
function updateWelcomeTaskStatus() {
    const taskStatusElement = document.getElementById('welcomeTaskStatus');
    const taskStatusIcon = document.getElementById('welcomeTaskStatusIcon');

    if (!taskStatusElement || !taskStatusIcon) return;

    // 检查是否有正在进行的任务
    const progressContainer = document.getElementById('progressContainer');

    if (progressContainer && !progressContainer.classList.contains('hidden')) {
        const progressText = document.getElementById('progressText');
        const currentProgress = progressText ? progressText.textContent : '进行中';
        taskStatusElement.textContent = currentProgress;
        taskStatusIcon.className = 'status-icon info';
        taskStatusIcon.textContent = '⚡';
    } else {
        taskStatusElement.textContent = '就绪';
        taskStatusIcon.className = 'status-icon success';
        taskStatusIcon.textContent = '✅';
    }
}

// 监听相关状态变化，自动更新欢迎面板
function initWelcomeStatusMonitoring() {
    // 监听书籍选择变化
    const bookSelect = document.getElementById('bookSelect');
    if (bookSelect) {
        bookSelect.addEventListener('change', updateWelcomeBookStatus);
    }

    // 监听API状态变化
    const apiStatusElement = document.getElementById('apiStatus');
    if (apiStatusElement) {
        const observer = new MutationObserver(updateWelcomeApiStatus);
        observer.observe(apiStatusElement, { childList: true, characterData: true, subtree: true });
    }

    // 监听文件信息变化
    const mappingFileInfo = document.getElementById('mappingFileInfo');
    if (mappingFileInfo) {
        const observer = new MutationObserver(updateWelcomeFileStatus);
        observer.observe(mappingFileInfo, { attributes: true, attributeFilter: ['class'] });
    }

    // 监听任务进度变化
    const progressContainer = document.getElementById('progressContainer');
    if (progressContainer) {
        const observer = new MutationObserver(updateWelcomeTaskStatus);
        observer.observe(progressContainer, { attributes: true, attributeFilter: ['class'] });
    }

    // 初始化状态
    updateWelcomeStatus();
}

// 在页面加载完成后初始化欢迎面板状态监控
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已经加载完成
    setTimeout(initWelcomeStatusMonitoring, 1000);

    // 初始化欢迎面板滚动功能
    initWelcomePanelScroll();
});

// ==================== 缺失函数的空实现 ====================

/**
 * 更新角色选择器（空实现，避免JavaScript错误）
 */
function updateCharacterSelect() {
    // TODO: 实现角色选择器更新逻辑
    // 目前为空实现，避免JavaScript错误
}

/**
 * 更新CV选择器（空实现，避免JavaScript错误）
 */
function updateCvSelect() {
    // TODO: 实现CV选择器更新逻辑
    // 目前为空实现，避免JavaScript错误
}

// ==================== 欢迎面板滚动功能 ====================

/**
 * 初始化欢迎面板滚动功能
 */
function initWelcomePanelScroll() {
    const welcomePanelContent = document.querySelector('.welcome-panel-content');

    if (!welcomePanelContent) {
        return;
    }

    // 监听滚动事件，添加滚动阴影效果
    welcomePanelContent.addEventListener('scroll', function() {
        const scrollTop = this.scrollTop;
        const scrollHeight = this.scrollHeight;
        const clientHeight = this.clientHeight;

        // 判断是否滚动到顶部或底部
        const isScrolledFromTop = scrollTop > 10;
        const isScrolledToBottom = scrollTop + clientHeight >= scrollHeight - 10;

        // 添加或移除滚动状态类
        if (isScrolledFromTop) {
            this.classList.add('scrolled');
        } else {
            this.classList.remove('scrolled');
        }

        // 可以在这里添加更多滚动相关的逻辑
        updateScrollIndicators(scrollTop, scrollHeight, clientHeight);
    });

    // 初始检查滚动状态
    const scrollTop = welcomePanelContent.scrollTop;
    const scrollHeight = welcomePanelContent.scrollHeight;
    const clientHeight = welcomePanelContent.clientHeight;

    if (scrollTop > 10) {
        welcomePanelContent.classList.add('scrolled');
    }

    updateScrollIndicators(scrollTop, scrollHeight, clientHeight);
}

/**
 * 更新滚动指示器
 */
function updateScrollIndicators(scrollTop, scrollHeight, clientHeight) {
    // 计算滚动进度
    const scrollProgress = scrollHeight > clientHeight ? scrollTop / (scrollHeight - clientHeight) : 0;

    // 可以在这里添加滚动进度指示器的逻辑
    // 例如更新进度条或其他视觉指示器

    // 触发自定义事件，其他组件可以监听
    const welcomePanel = document.querySelector('.welcome-panel');
    if (welcomePanel) {
        welcomePanel.dispatchEvent(new CustomEvent('welcomeScroll', {
            detail: {
                scrollTop,
                scrollHeight,
                clientHeight,
                scrollProgress
            }
        }));
    }
}

/**
 * 平滑滚动到欢迎面板的特定位置
 */
function scrollToWelcomeSection(sectionSelector) {
    const welcomePanelContent = document.querySelector('.welcome-panel-content');
    const targetSection = document.querySelector(sectionSelector);

    if (!welcomePanelContent || !targetSection) {
        return;
    }

    const targetOffset = targetSection.offsetTop - welcomePanelContent.offsetTop;

    welcomePanelContent.scrollTo({
        top: targetOffset,
        behavior: 'smooth'
    });
}

/**
 * 滚动到欢迎面板顶部
 */
function scrollToWelcomeTop() {
    const welcomePanelContent = document.querySelector('.welcome-panel-content');

    if (!welcomePanelContent) {
        return;
    }

    welcomePanelContent.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}
