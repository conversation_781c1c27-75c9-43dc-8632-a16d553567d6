"""
文件处理器

此模块提供文件上传、解析和验证功能。
"""
import os
import tempfile
import base64
from typing import Dict, Any
from ...utils.excel_handler import ExcelHandler
from ...services.config_service import ConfigService
from ...utils.helpers import format_success_response, format_error_response


def _get_last_excel_directory(config_service=None) -> str:
    """获取上次使用的Excel文件目录

    Args:
        config_service: 配置服务实例

    Returns:
        str: 目录路径，如果没有记录或路径不存在则返回默认路径
    """
    try:
        if not config_service:
            print("⚠️ [路径记忆] 未提供配置服务，使用默认路径")
            return _get_default_directory()

        # 从配置中获取上次使用的目录
        last_directory = config_service.get('FILES', 'last_excel_directory', '')
        print(f"🔍 [路径记忆] 配置中的目录: '{last_directory}'")

        if not last_directory:
            print("🔍 [路径记忆] 配置中无目录记录，使用默认路径")
            return _get_default_directory()

        # 检查目录是否存在
        if os.path.exists(last_directory) and os.path.isdir(last_directory):
            print(f"✅ [路径记忆] 目录存在，使用记忆路径: {last_directory}")
            return last_directory
        else:
            print(f"⚠️ [路径记忆] 记忆的目录不存在: {last_directory}，使用默认路径")
            return _get_default_directory()

    except Exception as e:
        print(f"❌ [路径记忆] 获取目录时出错: {e}，使用默认路径")
        return _get_default_directory()


def _save_last_excel_directory(config_service, directory_path: str) -> bool:
    """保存Excel文件目录到配置

    Args:
        config_service: 配置服务实例
        directory_path: 要保存的目录路径

    Returns:
        bool: 保存是否成功
    """
    try:
        if not config_service:
            print("⚠️ [路径记忆] 未提供配置服务，无法保存路径")
            return False

        if not directory_path or not os.path.exists(directory_path):
            print(f"⚠️ [路径记忆] 无效的目录路径: {directory_path}")
            return False

        # 保存到配置
        config_service.set('FILES', 'last_excel_directory', directory_path)
        config_service.save()
        print(f"✅ [路径记忆] 成功保存目录路径: {directory_path}")
        return True

    except Exception as e:
        print(f"❌ [路径记忆] 保存目录路径时出错: {e}")
        return False


def _get_default_directory() -> str:
    """获取默认的文件选择目录

    Returns:
        str: 默认目录路径
    """
    try:
        # 优先使用用户文档目录
        import os
        if os.name == 'nt':  # Windows
            documents_path = os.path.join(os.path.expanduser('~'), 'Documents')
        else:  # macOS/Linux
            documents_path = os.path.expanduser('~/Documents')

        if os.path.exists(documents_path):
            print(f"🔍 [路径记忆] 使用文档目录: {documents_path}")
            return documents_path
        else:
            # 如果文档目录不存在，使用用户主目录
            home_path = os.path.expanduser('~')
            print(f"🔍 [路径记忆] 使用主目录: {home_path}")
            return home_path

    except Exception as e:
        print(f"❌ [路径记忆] 获取默认目录时出错: {e}，使用当前目录")
        return os.getcwd()


def upload_excel_file(file_data: str, filename: str) -> Dict[str, Any]:
    """上传Excel文件
    
    Args:
        file_data: Base64编码的文件数据
        filename: 文件名
        
    Returns:
        Dict[str, Any]: 上传结果
    """
    try:
        # 验证文件扩展名
        if not filename.lower().endswith(('.xlsx', '.xls')):
            return format_error_response(
                error="不支持的文件格式",
                message="请上传Excel文件(.xlsx或.xls)"
            )
        
        # 解码文件数据
        try:
            file_bytes = base64.b64decode(file_data)
        except Exception as e:
            return format_error_response(
                error="文件数据解码失败",
                message="文件数据格式错误"
            )
        
        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"cv_assignment_{filename}")
        
        with open(temp_file_path, 'wb') as f:
            f.write(file_bytes)
        
        # 验证文件
        validation_result = ExcelHandler.validate_excel_file(temp_file_path)
        
        if not validation_result["valid"]:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            
            return format_error_response(
                error=validation_result["error"],
                message="Excel文件验证失败"
            )
        
        return format_success_response(
            data={
                "file_path": temp_file_path,
                "filename": filename,
                "sheets": validation_result["sheets"],
                "size": len(file_bytes)
            },
            message="文件上传成功"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="文件上传失败"
        )


def parse_excel_file(file_path: str, config_service: ConfigService) -> Dict[str, Any]:
    """解析Excel文件
    
    Args:
        file_path: Excel文件路径
        config_service: 配置服务实例
        
    Returns:
        Dict[str, Any]: 解析结果
    """
    try:
        if not os.path.exists(file_path):
            return format_error_response(
                error="文件不存在",
                message="请先上传Excel文件"
            )
        
        # 获取Excel配置
        excel_config = config_service.get_excel_config()
        
        # 读取角色-CV映射
        character_cv_map = ExcelHandler.read_character_cv_map(
            file_path=file_path,
            sheet_name=excel_config['sheet_name'],
            header_row=excel_config['header_row'],
            character_col_idx=excel_config['character_col_idx'],
            cv_col_idx=excel_config['cv_col_idx'],
            character_keyword=excel_config['character_col_keyword'],
            cv_keyword=excel_config['cv_col_keyword'],
            price_col_idx=excel_config.get('price_col_idx'),
            price_keyword=excel_config.get('price_col_keyword')
        )
        
        if not character_cv_map:
            return format_error_response(
                error="未找到有效的角色-CV映射",
                message="Excel文件中没有找到有效的数据"
            )
        
        # 分离CV名称和价格信息
        processed_mapping = {}
        for character, cv_info in character_cv_map.items():
            if '|' in cv_info:
                cv_name, price = cv_info.split('|', 1)
                processed_mapping[character] = {
                    "cv": cv_name.strip(),
                    "price": price.strip()
                }
            else:
                processed_mapping[character] = {
                    "cv": cv_info.strip(),
                    "price": ""
                }
        
        return format_success_response(
            data={
                "character_cv_mapping": processed_mapping,
                "count": len(processed_mapping),
                "file_path": file_path
            },
            message=f"成功解析{len(processed_mapping)}个角色-CV映射"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="解析Excel文件失败"
        )


def validate_excel_file(file_path: str, sheet_name: str = None) -> Dict[str, Any]:
    """验证Excel文件
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        
    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        validation_result = ExcelHandler.validate_excel_file(file_path, sheet_name)
        
        if validation_result["valid"]:
            return format_success_response(
                data=validation_result,
                message="Excel文件验证通过"
            )
        else:
            return format_error_response(
                error=validation_result["error"],
                message="Excel文件验证失败"
            )
            
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="验证Excel文件时发生错误"
        )


def get_excel_preview(file_path: str, sheet_name: str, max_rows: int = 10) -> Dict[str, Any]:
    """获取Excel文件预览
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        max_rows: 最大预览行数
        
    Returns:
        Dict[str, Any]: 预览数据
    """
    try:
        import pandas as pd
        
        if not os.path.exists(file_path):
            return format_error_response(
                error="文件不存在",
                message="请先上传Excel文件"
            )
        
        # 读取Excel文件的前几行
        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=max_rows)
        
        # 转换为前端可用的格式
        preview_data = {
            "columns": df.columns.tolist(),
            "rows": df.values.tolist(),
            "total_columns": len(df.columns),
            "preview_rows": len(df)
        }
        
        return format_success_response(
            data=preview_data,
            message=f"成功获取Excel预览数据（前{len(df)}行）"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取Excel预览失败"
        )


def validate_excel_file_from_data(file_data: str, filename: str) -> Dict[str, Any]:
    """从Base64数据验证Excel文件

    Args:
        file_data: Base64编码的文件数据
        filename: 文件名

    Returns:
        Dict[str, Any]: 验证结果，包含工作表列表
    """
    try:
        # 验证文件扩展名
        if not filename.lower().endswith(('.xlsx', '.xls')):
            return format_error_response(
                error="不支持的文件格式",
                message="请上传Excel文件(.xlsx或.xls)"
            )

        # 解码文件数据
        try:
            file_bytes = base64.b64decode(file_data)
        except Exception as e:
            return format_error_response(
                error="文件数据解码失败",
                message="文件数据格式错误"
            )

        # 使用临时文件避免文件名冲突和锁定问题
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_bytes)
            temp_file_path = temp_file.name

        try:
            # 验证文件
            validation_result = ExcelHandler.validate_excel_file(temp_file_path)
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass  # 忽略删除失败

        if not validation_result["valid"]:
            return format_error_response(
                error=validation_result["error"],
                message="Excel文件验证失败"
            )

        return format_success_response(
            data={
                "sheets": validation_result["sheets"],
                "filename": filename,
                "size": len(file_bytes)
            },
            message="Excel文件验证通过"
        )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="验证Excel文件失败"
        )


def parse_excel_mapping_from_data(file_data: str, filename: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """从Base64数据解析Excel角色-CV映射

    Args:
        file_data: Base64编码的文件数据
        filename: 文件名
        config: 解析配置
            - sheet_name: 工作表名称
            - header_row: 表头行号
            - character_keyword: 角色列关键词
            - cv_keyword: CV列关键词
            - price_keyword: 价格列关键词（可选）

    Returns:
        Dict[str, Any]: 解析结果
    """
    try:
        # 解码文件数据
        try:
            file_bytes = base64.b64decode(file_data)
        except Exception as e:
            return format_error_response(
                error="文件数据解码失败",
                message="文件数据格式错误"
            )

        # 使用临时文件避免文件名冲突和锁定问题
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_bytes)
            temp_file_path = temp_file.name

        try:
            # 读取角色-CV映射
            character_cv_map = ExcelHandler.read_character_cv_map(
                file_path=temp_file_path,
                sheet_name=config.get('sheet_name', ''),
                header_row=config.get('header_row', 0),
                character_col_idx=0,  # 回退索引
                cv_col_idx=1,  # 回退索引
                character_keyword=config.get('character_keyword', ''),
                cv_keyword=config.get('cv_keyword', ''),
                price_col_idx=2,  # 回退索引
                price_keyword=config.get('price_keyword', '')
            )

            if not character_cv_map:
                return format_error_response(
                    error="未找到有效的角色-CV映射",
                    message="Excel文件中没有找到有效的数据"
                )

            # 转换为前端需要的格式
            mapping_data = []
            for character_name, cv_info in character_cv_map.items():
                mapping_data.append({
                    "character_name": character_name,
                    "cv_info": cv_info
                })

            return format_success_response(
                data=mapping_data,
                message=f"成功解析{len(mapping_data)}个角色-CV映射"
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass  # 忽略删除失败

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="解析Excel文件失败"
        )


def preview_excel_columns_from_data(file_data: str, filename: str, sheet_name: str, header_row: int = 0) -> Dict[str, Any]:
    """从Base64数据预览Excel列结构

    Args:
        file_data: Base64编码的文件数据
        filename: 文件名
        sheet_name: 工作表名称
        header_row: 表头行号

    Returns:
        Dict[str, Any]: 列结构预览
    """
    try:
        # 解码文件数据
        try:
            file_bytes = base64.b64decode(file_data)
        except Exception as e:
            return format_error_response(
                error="文件数据解码失败",
                message="文件数据格式错误"
            )

        # 使用临时文件避免文件名冲突和锁定问题
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_bytes)
            temp_file_path = temp_file.name

        try:
            import pandas as pd

            # 读取Excel文件的表头
            df = pd.read_excel(temp_file_path, sheet_name=sheet_name, header=header_row, nrows=0)

            columns = df.columns.tolist()

            return format_success_response(
                data={
                    "columns": columns,
                    "total_columns": len(columns),
                    "sheet_name": sheet_name,
                    "header_row": header_row
                },
                message=f"成功获取{len(columns)}个列信息"
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass  # 忽略删除失败

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="预览Excel列结构失败"
        )


def resolve_file_path(file_path: str) -> str:
    """解析文件路径，尝试找到实际的文件位置

    Args:
        file_path: 原始文件路径

    Returns:
        str: 解析后的文件路径
    """
    print(f"🔍 [后端] resolve_file_path 输入: {file_path}")

    # 如果是绝对路径且存在，直接返回
    if os.path.isabs(file_path) and os.path.exists(file_path):
        print(f"🔍 [后端] 找到绝对路径: {file_path}")
        return file_path

    # 如果只是文件名，尝试在常见目录中查找
    if not os.path.dirname(file_path):
        filename = file_path
        print(f"🔍 [后端] 搜索文件名: {filename}")

        # 扩展搜索目录列表，包含更多常见位置
        search_dirs = [
            os.getcwd(),  # 当前工作目录
            os.path.expanduser("~/Desktop"),  # 桌面
            os.path.expanduser("~/Downloads"),  # 下载目录
            os.path.expanduser("~/Documents"),  # 文档目录
            os.path.expanduser("~"),  # 用户主目录
            tempfile.gettempdir(),  # 临时目录
            # Windows特定目录
            os.path.expanduser("~/OneDrive"),  # OneDrive
            os.path.expanduser("~/OneDrive/Desktop"),  # OneDrive桌面
            os.path.expanduser("~/OneDrive/Documents"),  # OneDrive文档
            # 项目相关目录
            os.path.dirname(os.getcwd()),  # 上级目录
            os.path.join(os.getcwd(), "data"),  # data目录
            os.path.join(os.getcwd(), "files"),  # files目录
        ]

        # 添加所有驱动器的常见目录（Windows）
        if os.name == 'nt':
            for drive in ['C:', 'D:', 'E:', 'F:']:
                if os.path.exists(drive + '\\'):
                    search_dirs.extend([
                        os.path.join(drive, '\\Users\\Public\\Desktop'),
                        os.path.join(drive, '\\Users\\Public\\Documents'),
                        os.path.join(drive, '\\temp'),
                        os.path.join(drive, '\\tmp'),
                    ])

        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                potential_path = os.path.join(search_dir, filename)
                print(f"🔍 [后端] 检查路径: {potential_path}")
                if os.path.exists(potential_path):
                    print(f"✅ [后端] 找到文件: {potential_path}")
                    return potential_path

        # 如果还是找不到，尝试递归搜索用户目录的第一层子目录
        print(f"🔍 [后端] 在用户目录的子目录中递归搜索...")
        user_home = os.path.expanduser("~")
        try:
            for item in os.listdir(user_home):
                item_path = os.path.join(user_home, item)
                if os.path.isdir(item_path) and not item.startswith('.'):
                    potential_path = os.path.join(item_path, filename)
                    if os.path.exists(potential_path):
                        print(f"✅ [后端] 在子目录中找到文件: {potential_path}")
                        return potential_path
        except (PermissionError, OSError):
            pass  # 忽略权限错误

    # 如果是相对路径，尝试相对于当前工作目录
    if not os.path.isabs(file_path):
        abs_path = os.path.abspath(file_path)
        print(f"🔍 [后端] 尝试绝对路径: {abs_path}")
        if os.path.exists(abs_path):
            print(f"🔍 [后端] 找到绝对路径: {abs_path}")
            return abs_path

    # 返回原始路径
    print(f"🔍 [后端] 未找到文件，返回原始路径: {file_path}")
    return file_path


def validate_excel_file_from_path(file_path: str) -> Dict[str, Any]:
    """从文件路径验证Excel文件

    Args:
        file_path: Excel文件路径

    Returns:
        Dict[str, Any]: 验证结果，包含工作表列表
    """
    try:
        print(f"🔧 [后端] validate_excel_file_from_path 输入: {file_path}")

        # 尝试解析文件路径
        resolved_path = resolve_file_path(file_path)
        print(f"🔧 [后端] 解析后的路径: {resolved_path}")

        if not os.path.exists(resolved_path):
            print(f"❌ [后端] 文件不存在: {resolved_path}")
            return format_error_response(
                error="文件不存在",
                message=f"找不到文件: {file_path} (已尝试解析为: {resolved_path})"
            )

        # 验证文件扩展名
        if not resolved_path.lower().endswith(('.xlsx', '.xls')):
            return format_error_response(
                error="不支持的文件格式",
                message="请选择Excel文件(.xlsx或.xls)"
            )

        # 验证文件
        print(f"🔧 [后端] 开始验证Excel文件: {resolved_path}")
        validation_result = ExcelHandler.validate_excel_file(resolved_path)
        print(f"🔧 [后端] 验证结果: valid={validation_result['valid']}, sheets={validation_result.get('sheets', [])}")

        if not validation_result["valid"]:
            print(f"❌ [后端] Excel验证失败: {validation_result['error']}")
            return format_error_response(
                error=validation_result["error"],
                message="Excel文件验证失败"
            )

        print(f"✅ [后端] Excel验证通过，发现{len(validation_result['sheets'])}个工作表")
        return format_success_response(
            data={
                "sheets": validation_result["sheets"],
                "filename": os.path.basename(resolved_path),
                "file_path": resolved_path
            },
            message="Excel文件验证通过"
        )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="验证Excel文件失败"
        )


def parse_excel_mapping_from_path(file_path: str, config_service: ConfigService) -> Dict[str, Any]:
    """从文件路径解析Excel角色-CV映射（使用配置文件中的设置）

    Args:
        file_path: Excel文件路径
        config_service: 配置服务实例

    Returns:
        Dict[str, Any]: 解析结果
    """
    try:
        print(f"📊 [后端] parse_excel_mapping_from_path 输入: 文件={file_path}")

        # 检查是否是绝对路径
        if os.path.isabs(file_path) and os.path.exists(file_path):
            # 如果是绝对路径且存在，直接使用
            resolved_path = file_path
            print(f"📊 [后端] 使用绝对路径: {resolved_path}")
        else:
            # 否则尝试解析文件路径
            resolved_path = resolve_file_path(file_path)
            print(f"📊 [后端] 解析后的路径: {resolved_path}")

        if not os.path.exists(resolved_path):
            print(f"❌ [后端] 文件不存在: {resolved_path}")
            return format_error_response(
                error="文件不存在",
                message=f"找不到文件: {file_path} (已尝试解析为: {resolved_path})"
            )

        # 从配置文件获取所有设置
        excel_config = config_service.get_excel_config()
        print(f"📊 [后端] 配置文件设置: {excel_config}")

        # 使用配置文件中的设置，确保数值类型正确
        header_row_raw = excel_config.get('header_row', 0)

        # 确保header_row是整数类型
        if isinstance(header_row_raw, str):
            try:
                header_row = int(header_row_raw)
                print(f"📊 [后端] 转换header_row: '{header_row_raw}' -> {header_row}")
            except ValueError:
                header_row = 0
                print(f"⚠️ [后端] header_row转换失败，使用默认值: {header_row}")
        else:
            header_row = int(header_row_raw) if header_row_raw is not None else 0

        config = {
            'sheet_name': excel_config.get('character_sheet_name', '角色'),  # 使用正确的配置键
            'header_row': header_row,
            'character_keyword': excel_config.get('character_column_keyword', '角色名称'),  # 修正键名
            'cv_keyword': excel_config.get('cv_column_keyword', '主播'),  # 修正键名
            'price_keyword': excel_config.get('price_column_keyword', '主播价格')  # 修正键名
        }
        print(f"📊 [后端] 使用的配置: {config}")
        print(f"📊 [后端] header_row类型: {type(config['header_row'])}")

        # 读取角色-CV映射
        print(f"📊 [后端] 开始读取Excel文件: {resolved_path}")
        character_cv_map = ExcelHandler.read_character_cv_map(
            file_path=resolved_path,
            sheet_name=config['sheet_name'],
            header_row=config['header_row'],
            character_col_idx=0,  # 回退索引
            cv_col_idx=1,  # 回退索引
            character_keyword=config['character_keyword'],
            cv_keyword=config['cv_keyword'],
            price_col_idx=2,  # 回退索引
            price_keyword=config['price_keyword']
        )

        if not character_cv_map:
            print(f"❌ [后端] 未找到有效的角色-CV映射")
            return format_error_response(
                error="未找到有效的角色-CV映射",
                message="Excel文件中没有找到有效的数据"
            )

        print(f"✅ [后端] 成功读取{len(character_cv_map)}个角色-CV映射")

        # 转换为前端需要的格式
        mapping_data = []
        for character_name, cv_info in character_cv_map.items():
            mapping_data.append({
                "character_name": character_name,
                "cv_info": cv_info
            })

        print(f"📊 [后端] 转换后的映射数据: {len(mapping_data)}条记录")
        # 打印前5条记录作为示例
        for i, item in enumerate(mapping_data[:5]):
            print(f"📊 [后端] 示例数据 {i+1}: {item}")
        if len(mapping_data) > 5:
            print(f"📊 [后端] ... 还有{len(mapping_data)-5}条记录")

        return format_success_response(
            data=mapping_data,
            message=f"成功解析{len(mapping_data)}个角色-CV映射"
        )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="解析Excel文件失败"
        )


def preview_excel_columns_from_path(file_path: str, sheet_name: str, header_row: int = 0) -> Dict[str, Any]:
    """从文件路径预览Excel列结构

    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        header_row: 表头行号

    Returns:
        Dict[str, Any]: 列结构预览
    """
    try:
        # 尝试解析文件路径
        resolved_path = resolve_file_path(file_path)

        if not os.path.exists(resolved_path):
            return format_error_response(
                error="文件不存在",
                message=f"找不到文件: {file_path} (已尝试解析为: {resolved_path})"
            )

        import pandas as pd

        # 读取Excel文件的表头
        df = pd.read_excel(resolved_path, sheet_name=sheet_name, header=header_row, nrows=0)

        columns = df.columns.tolist()

        return format_success_response(
            data={
                "columns": columns,
                "total_columns": len(columns),
                "sheet_name": sheet_name,
                "header_row": header_row
            },
            message=f"成功获取{len(columns)}个列信息"
        )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="预览Excel列结构失败"
        )


def select_excel_file_with_pywebview(config_service=None) -> Dict[str, Any]:
    """使用pywebview的文件对话框选择Excel文件，支持路径记忆功能

    Args:
        config_service: 配置服务实例，用于读取和保存路径记忆

    Returns:
        Dict[str, Any]: 选择结果，包含文件路径和基本信息
    """
    try:
        import webview
        import os

        print(f"🔍 [后端] webview模块导入成功")
        print(f"🔍 [后端] webview.windows: {webview.windows}")
        print(f"🔍 [后端] webview.windows长度: {len(webview.windows) if webview.windows else 0}")

        # 获取上次使用的目录路径
        initial_directory = _get_last_excel_directory(config_service)
        print(f"🔍 [后端] 获取到的初始目录: {initial_directory}")

        # 使用pywebview的文件选择对话框
        # 根据pywebview文档，file_types应该是一个元组列表
        file_types = [
            ('Excel文件', '*.xlsx;*.xls'),
            ('Excel 2007+', '*.xlsx'),
            ('Excel 97-2003', '*.xls'),
            ('所有文件', '*.*')
        ]

        print(f"🔍 [后端] 文件类型过滤器: {file_types}")

        try:
            print(f"🔍 [后端] 准备调用create_file_dialog...")

            # 先尝试不使用文件类型过滤器和初始目录
            try:
                print(f"🔍 [后端] 尝试基础调用...")
                if initial_directory:
                    print(f"🔍 [后端] 尝试使用初始目录: {initial_directory}")
                    result = webview.windows[0].create_file_dialog(
                        webview.OPEN_DIALOG,
                        allow_multiple=False,
                        directory=initial_directory
                    )
                else:
                    result = webview.windows[0].create_file_dialog(
                        webview.OPEN_DIALOG,
                        allow_multiple=False
                    )
                print(f"🔍 [后端] 基础调用成功")
            except Exception as basic_error:
                print(f"⚠️ [后端] 基础调用失败: {basic_error}")
                # 尝试使用文件类型过滤器
                print(f"🔍 [后端] 尝试使用文件类型过滤器...")
                try:
                    if initial_directory:
                        result = webview.windows[0].create_file_dialog(
                            webview.OPEN_DIALOG,
                            allow_multiple=False,
                            file_types=file_types,
                            directory=initial_directory
                        )
                    else:
                        result = webview.windows[0].create_file_dialog(
                            webview.OPEN_DIALOG,
                            allow_multiple=False,
                            file_types=file_types
                        )
                    print(f"🔍 [后端] 有过滤器调用成功")
                except Exception as filter_error:
                    print(f"⚠️ [后端] 有过滤器调用也失败: {filter_error}")
                    # 最后尝试最简单的调用
                    result = webview.windows[0].create_file_dialog(
                        webview.OPEN_DIALOG,
                        allow_multiple=False
                    )
                    print(f"🔍 [后端] 简单调用成功")

        except Exception as dialog_error:
            print(f"❌ [后端] create_file_dialog调用失败: {dialog_error}")
            print(f"❌ [后端] 错误类型: {type(dialog_error)}")
            import traceback
            print(f"❌ [后端] 错误堆栈: {traceback.format_exc()}")
            raise dialog_error

        print(f"🔍 [后端] 文件对话框返回结果: {result}")
        print(f"🔍 [后端] 结果类型: {type(result)}")

        if not result or len(result) == 0:
            return format_error_response(
                error="未选择文件",
                message="用户取消了文件选择"
            )

        file_path = result[0]
        print(f"🔍 [后端] 提取的文件路径: {file_path}")
        print(f"🔍 [后端] 文件路径类型: {type(file_path)}")

        # 确保file_path是字符串类型
        if not isinstance(file_path, str):
            file_path = str(file_path)
            print(f"🔍 [后端] 转换后的文件路径: {file_path}")

        # 验证文件
        print(f"🔍 [后端] 检查文件是否存在: {file_path}")
        if not os.path.exists(file_path):
            return format_error_response(
                error="文件不存在",
                message=f"选择的文件不存在: {file_path}"
            )

        # 验证文件类型
        print(f"🔍 [后端] 验证文件类型: {file_path}")
        print(f"🔍 [后端] file_path类型: {type(file_path)}")

        # 确保file_path是字符串类型
        if not isinstance(file_path, str):
            print(f"⚠️ [后端] file_path不是字符串类型，进行转换")
            file_path = str(file_path)
            print(f"🔍 [后端] 转换后的file_path: {file_path}")

        if not file_path.lower().endswith(('.xlsx', '.xls')):
            return format_error_response(
                error="不支持的文件格式",
                message="请选择Excel文件(.xlsx或.xls)"
            )

        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)

        print(f"✅ [后端] 文件验证通过: {file_name}, 大小: {file_size} bytes")

        # 保存文件所在目录到配置中，用于下次记忆
        file_directory = os.path.dirname(file_path)
        _save_last_excel_directory(config_service, file_directory)
        print(f"💾 [后端] 已保存目录路径到配置: {file_directory}")

        return format_success_response(
            data={
                "file_path": file_path,
                "filename": file_name,
                "size": file_size,
                "directory": file_directory
            },
            message=f"成功选择文件: {file_name}"
        )

    except ImportError:
        return format_error_response(
            error="缺少webview模块",
            message="无法使用文件选择对话框，请确保在pywebview环境中运行"
        )
    except Exception as e:
        print(f"❌ [后端] 文件选择对话框错误: {str(e)}")
        return format_error_response(
            error=str(e),
            message="打开文件选择对话框失败"
        )


def cleanup_temp_file(file_path: str) -> Dict[str, Any]:
    """清理临时文件

    Args:
        file_path: 文件路径

    Returns:
        Dict[str, Any]: 清理结果
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return format_success_response(
                data={"file_path": file_path},
                message="临时文件清理成功"
            )
        else:
            return format_success_response(
                data={"file_path": file_path},
                message="文件不存在，无需清理"
            )

    except Exception as e:
        return format_error_response(
            error=str(e),
            message="清理临时文件失败"
        )
