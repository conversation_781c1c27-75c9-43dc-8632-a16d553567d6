/*
 * CV List Panel - CV列表面板样式
 * 包含CV列表页面的所有样式：面板容器、头部、表格、分页等
 * 提供CV列表管理功能的界面样式
 */

/* ==================== CV列表页面样式 ==================== */
.cv-list-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-sm);
    overflow: hidden;
    box-sizing: border-box;
}

.cv-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.cv-list-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.cv-list-actions {
    display: flex;
    align-items: center;
}

.cv-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.cv-list-table-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 固定表头的表格样式 */
.cv-list-table-wrapper .mapping-table {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0;
    border-radius: 0;
    border: none;
}

.cv-list-table-wrapper .mapping-table thead {
    flex-shrink: 0;
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.cv-list-table-wrapper .mapping-table thead th {
    padding: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: sticky;
    top: 0;
}

.cv-list-table-wrapper .mapping-table tbody {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar {
    width: 8px;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
    background: transparent;
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.cv-list-table-wrapper .mapping-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.cv-list-table-wrapper .mapping-table tbody td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    vertical-align: middle;
}

/* 固定分页控件样式 */
.cv-list-pagination-fixed {
    flex-shrink: 0;
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    position: relative;
    z-index: 5;
    width: 100%;
    box-sizing: border-box;
}

/* 空状态样式 */
.cv-list-table-wrapper p {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    margin: 0;
    font-style: italic;
}
