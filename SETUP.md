# 角色CV分配工具 - 环境配置指南

## 📋 配置文件设置

### 1. 创建配置文件

#### 方法一：使用配置脚本（推荐）

运行自动配置脚本，按提示设置：

```bash
python setup-config.py
```

#### 方法二：手动复制配置文件

首次使用时，请复制示例配置文件并修改为您的实际配置：

```bash
# 复制示例配置文件
cp config.example.json config.json
```

### 2. 配置API Token

编辑 `config.json` 文件，将 `YOUR_API_TOKEN_HERE` 替换为您的真实API Token：

```json
{
    "API": {
        "base_url": "https://www.gstudios.com.cn/story_v2/api",
        "default_token": "您的真实API_Token",
        "enable_real_assignment": "false"
    }
}
```

### 3. 安全注意事项

⚠️ **重要提醒**：
- `config.json` 文件包含敏感信息，已被 `.gitignore` 排除
- 请勿将包含真实API Token的配置文件提交到版本控制
- 如需分享配置，请使用 `config.example.json` 模板

## 🚀 快速启动

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **配置API Token**：
   - 复制 `config.example.json` 为 `config.json`
   - 在 `config.json` 中设置您的API Token

3. **启动应用**：
   ```bash
   python run.py
   ```

## 📁 项目结构

```
assign-cv-to-characters/
├── .gitignore              # Git忽略文件配置
├── config.example.json     # 配置文件模板
├── config.json            # 实际配置文件（不会被提交）
├── requirements.txt        # Python依赖
├── pyproject.toml         # 项目配置
├── run.py                 # 启动脚本
└── src/                   # 源代码目录
```

## 🔒 敏感信息保护

本项目的 `.gitignore` 文件已配置为：
- ✅ 排除包含API Token的配置文件
- ✅ 排除用户上传的Excel文件
- ✅ 排除Python缓存和虚拟环境
- ✅ 排除IDE配置文件
- ✅ 保留重要的项目文件和依赖配置

## 📝 配置说明

### API配置
- `base_url`: GStudios API服务器地址
- `default_token`: 您的API访问令牌
- `enable_real_assignment`: 是否启用真实分配模式

### Excel配置
- `character_column_keyword`: 角色列识别关键词
- `cv_column_keyword`: CV列识别关键词
- `price_column_keyword`: 价格列识别关键词
- `header_row`: Excel表头所在行号

### 文件路径配置
- `cv_config_file`: CV简名映射文件路径
- `last_excel_directory`: 上次使用的Excel文件目录
