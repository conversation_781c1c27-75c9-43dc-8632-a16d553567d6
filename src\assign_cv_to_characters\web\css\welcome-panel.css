/*
 * Welcome Panel - 欢迎面板样式
 * 包含欢迎页面的所有样式：欢迎内容、功能特性、仪表板布局等
 * 提供应用首页的展示和介绍功能
 */

/* ==================== Welcome Panel ==================== */
.welcome-panel {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-glass);
    margin-bottom: var(--spacing-xl);
    position: relative;
    height: 100%;
    max-height: calc(100vh - 180px); /* 减去头部、脚部和边距的高度 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 欢迎面板内容容器 - 支持垂直滚动 */
.welcome-panel-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-xl);
    padding-top: calc(var(--spacing-xl) + 4px); /* 为顶部装饰条留出空间 */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

/* 现代化自定义滚动条样式 */
.welcome-panel-content::-webkit-scrollbar {
    width: 8px;
}

.welcome-panel-content::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

.welcome-panel-content::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    opacity: 0.6;
    transition: all var(--transition-smooth);
}

.welcome-panel-content::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
    opacity: 0.8;
    transform: scaleX(1.2);
}

/* 滚动条角落 */
.welcome-panel-content::-webkit-scrollbar-corner {
    background: transparent;
}

/* 平滑滚动 */
.welcome-panel-content {
    scroll-behavior: smooth;
}

/* 滚动时的阴影效果 */
.welcome-panel-content.scrolled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to bottom, var(--bg-glass), transparent);
    pointer-events: none;
    z-index: 10;
}

.welcome-panel-content.scrolled::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to top, var(--bg-glass), transparent);
    pointer-events: none;
    z-index: 10;
}

.welcome-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    z-index: 1;
}

/* ==================== Welcome Header ==================== */
.welcome-header {
    text-align: center;
    padding: var(--spacing-2xl) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
    animation: welcomeIconPulse 2s ease-in-out infinite;
}

@keyframes welcomeIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-version {
    display: inline-block;
    background: var(--primary-gradient);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-button);
}

.welcome-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ==================== Features Grid ==================== */
.welcome-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.feature-card {
    background: var(--bg-glass-secondary);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-smooth);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    display: block;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.feature-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ==================== Quick Start Guide ==================== */
.quick-start {
    background: var(--bg-glass-secondary);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.quick-start-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.quick-start-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.step-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-smooth);
}

.step-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
    border-color: var(--border-glass-hover);
}

.step-number {
    width: 32px;
    height: 32px;
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.step-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* ==================== Action Buttons ==================== */
.welcome-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.welcome-btn {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-button);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-smooth);
    box-shadow: var(--shadow-button);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.welcome-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-smooth);
}

.welcome-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
}

.welcome-btn:hover::before {
    left: 100%;
}

.welcome-btn.secondary {
    background: var(--bg-glass);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
    box-shadow: var(--shadow-md);
}

.welcome-btn.secondary:hover {
    background: var(--bg-glass-hover);
    border-color: var(--border-glass-hover);
}

/* ==================== Status Dashboard ==================== */
.status-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.status-card {
    background: var(--bg-glass-secondary);
    backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-smooth);
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
}

.status-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    display: block;
}

.status-icon.success {
    color: var(--success-color);
}

.status-icon.warning {
    color: var(--warning-color);
}

.status-icon.error {
    color: var(--error-color);
}

.status-icon.info {
    color: var(--info-color);
}

.status-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.status-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* ==================== Responsive Design ==================== */
@media (max-width: 768px) {
    .welcome-panel {
        margin: var(--spacing-md);
        max-height: calc(100vh - 160px); /* 移动端调整高度 */
    }

    .welcome-panel-content {
        padding: var(--spacing-lg);
        padding-top: calc(var(--spacing-lg) + 4px);
    }

    /* 移动端滚动条样式调整 */
    .welcome-panel-content::-webkit-scrollbar {
        width: 6px; /* 移动端更细的滚动条 */
    }

    .welcome-header {
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .quick-start-steps {
        grid-template-columns: 1fr;
    }

    .welcome-actions {
        flex-direction: column;
        align-items: center;
    }

    .welcome-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .status-dashboard {
        grid-template-columns: 1fr;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .welcome-panel {
        max-height: calc(100vh - 140px);
    }

    .welcome-panel-content {
        padding: var(--spacing-md);
        padding-top: calc(var(--spacing-md) + 4px);
    }

    /* 超小屏幕隐藏滚动条，使用原生滚动 */
    .welcome-panel-content::-webkit-scrollbar {
        width: 0px;
        background: transparent;
    }
}
