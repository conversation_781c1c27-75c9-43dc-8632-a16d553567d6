# 角色CV分配工具 Template - Project Configuration
# 基于 Augment-Code-Free 架构的现代GUI应用模板

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "assign-cv-to-characters"  # 修改为您的项目名称
version = "0.1.0"
description = "A modern GUI application built with pywebview"  # 修改为您的项目描述
readme = "README.md"
license = "MIT"
requires-python = ">=3.10"
authors = [
    { name = "角色CV分配工具开发团队", email = "<EMAIL>" }  # 修改为您的信息
]
keywords = ["gui", "desktop", "pywebview", "modern", "cross-platform"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Desktop Environment",
]

# 核心依赖 - 基于 Augment-Code-Free 的最佳实践
dependencies = [
    "pywebview>=4.4.0",  # 核心GUI框架
    "jinja2>=3.1.0",     # 模板引擎（可选，用于动态HTML生成）
]

# 可选依赖组
[project.optional-dependencies]
dev = [
    "black>=23.0.0",      # 代码格式化
    "isort>=5.12.0",      # 导入排序
    "flake8>=6.0.0",      # 代码检查
    "pytest>=7.0.0",      # 测试框架
    "pytest-cov>=4.0.0",  # 测试覆盖率
]

# 如果需要数据库支持
database = [
    # "sqlite3",            # SQLite支持（Python内置，无需安装）
    # "sqlalchemy>=2.0.0", # ORM框架（可选）
]

# 如果需要网络功能
network = [
    "requests>=2.28.0",   # HTTP客户端
    "aiohttp>=3.8.0",     # 异步HTTP（可选）
]

# 如果需要配置文件支持
config = [
    "pyyaml>=6.0",        # YAML配置文件
    "toml>=0.10.0",       # TOML配置文件
]

# 命令行入口点
[project.scripts]
assign-cv-to-characters = "assign_cv_to_characters.main:main"  # 修改为您的包名

# 项目URL
[project.urls]
Homepage = "https://github.com/cv-assignment-team/assign-cv-to-characters"  # 修改为您的仓库
Repository = "https://github.com/cv-assignment-team/assign-cv-to-characters"
Issues = "https://github.com/cv-assignment-team/assign-cv-to-characters/issues"
Documentation = "https://github.com/cv-assignment-team/assign-cv-to-characters/docs"

# 构建配置
[tool.hatch.build.targets.wheel]
packages = ["src/assign_cv_to_characters"]  # 修改为您的包名

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/LICENSE",
    "/requirements.txt",
]

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
)/
'''

# 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["assign_cv_to_characters"]  # 修改为您的包名

# 测试配置
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/assign_cv_to_characters",  # 修改为您的包名
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]

# 覆盖率配置
[tool.coverage.run]
source = ["src/assign_cv_to_characters"]  # 修改为您的包名
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# 类型检查配置（如果使用 mypy）
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "webview.*",
    "jinja2.*",
]
ignore_missing_imports = true

# UV包管理器配置
# 简化配置以避免兼容性问题
