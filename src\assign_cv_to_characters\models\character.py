"""
角色数据模型

此模块定义了角色(Character)的数据模型。
"""
from typing import Optional, Dict, Any


class Character:
    """角色数据模型
    
    表示一个角色及其属性，包括ID、名称和关联的CV ID。
    
    Attributes:
        id (str): 角色ID
        name (str): 角色名称
        cv_human_id (Optional[str]): 人工配音ID，如果未分配则为None
    """
    
    def __init__(self, id: str, name: str, cv_human_id: Optional[str] = None):
        """初始化角色对象
        
        Args:
            id: 角色ID
            name: 角色名称
            cv_human_id: 人工配音ID，默认为None
        """
        self.id = id
        self.name = name
        self.cv_human_id = cv_human_id
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """从API返回的字典创建角色对象
        
        Args:
            data: 包含角色数据的字典
            
        Returns:
            Character: 创建的角色对象
        """
        return cls(
            id=data.get('id', ''),
            name=data.get('name', ''),
            cv_human_id=data.get('cvHumanId')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 表示角色的字典
        """
        return {
            'id': self.id,
            'name': self.name,
            'cvHumanId': self.cv_human_id
        }
    
    def __str__(self) -> str:
        """字符串表示
        
        Returns:
            str: 角色的字符串表示
        """
        return f"Character(id={self.id}, name={self.name}, cv_human_id={self.cv_human_id})"
