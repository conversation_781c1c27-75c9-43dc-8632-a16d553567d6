<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色CV分配工具</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 页面特定样式覆盖 - 仅保留必要的特殊样式 */

        /* 设置页面特殊布局修复 - 由于复杂的TAB嵌套结构，需要特殊处理 */
        #settingsTab.tab-content.active {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            height: 100%;
        }

        /* 设置页面TAB内容区域特殊布局 - 由于复杂嵌套结构需要保留 */
        #settingsTab.tab-content.active .config-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn" title="打开菜单">☰</button>
                    <button class="sidebar-toggle-header" id="sidebarToggle" title="展开/折叠菜单">
                        <span class="toggle-icon">◀</span>
                    </button>
                    <div class="header-title-group">
                        <h1>🎭 角色CV分配工具</h1>
                        <p class="subtitle">智能化角色配音分配管理系统</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="book-selection-header">
                        <label for="bookSelect" class="book-label">书籍:</label>
                        <div class="book-controls-header">
                            <select id="bookSelect" class="book-select-header" onchange="onBookSelectionChange()">
                                <option value="">请先设置API Token</option>
                            </select>
                            <button class="btn-header btn-refresh" onclick="loadBooks()" title="刷新书籍列表">🔄</button>
                        </div>
                    </div>
                    <div class="status-indicator">
                        <span class="status-label">状态:</span>
                        <span class="status-value" id="apiStatus">检查中...</span>
                    </div>
                    <button class="about-btn" onclick="showAboutModal()" title="关于应用">ℹ️</button>
                </div>
            </div>
        </header>

        <!-- 中部区域 - 包含侧边栏和主内容 -->
        <div class="layout-main">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="switchTab('welcome')" data-tab="welcome">
                            <span class="nav-icon">🏠</span>
                            <span class="nav-text">欢迎首页</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('mapping')" data-tab="mapping">
                            <span class="nav-icon">🎭</span>
                            <span class="nav-text">角色-CV映射</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('cvlist')" data-tab="cvlist">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">CV列表</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('nicknames')" data-tab="nicknames">
                            <span class="nav-icon">📝</span>
                            <span class="nav-text">CV简名管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('assignment')" data-tab="assignment">
                            <span class="nav-icon">⚡</span>
                            <span class="nav-text">分配任务</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('logs')" data-tab="logs">
                            <span class="nav-icon">📋</span>
                            <span class="nav-text">操作日志</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('settings')" data-tab="settings">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">基础设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

            <!-- Main Content -->
            <main class="main-content">
            <!-- 欢迎首页标签页 -->
            <div id="welcomeTab" class="tab-content active">
                <div class="welcome-panel">
                    <div class="welcome-panel-content">
                        <!-- 欢迎头部 -->
                        <div class="welcome-header">
                        <div class="welcome-icon">🎭</div>
                        <h1 class="welcome-title">角色CV分配工具</h1>
                        <div class="welcome-version">v2.0.0</div>
                        <p class="welcome-description">
                            智能化角色配音分配管理系统，提供现代化的用户界面和强大的批量处理功能，
                            让角色与CV的匹配变得更加高效和精准。
                        </p>
                    </div>

                    <!-- 功能特性展示 -->
                    <div class="welcome-features">
                        <div class="feature-card">
                            <div class="feature-icon">🤖</div>
                            <div class="feature-title">智能分配</div>
                            <div class="feature-description">基于AI算法的智能角色CV匹配，提高分配效率和准确性</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">📊</div>
                            <div class="feature-title">Excel处理</div>
                            <div class="feature-description">支持Excel文件的导入解析，批量处理角色数据</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">👥</div>
                            <div class="feature-title">CV管理</div>
                            <div class="feature-description">完整的CV信息管理，支持简名映射和数据维护</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">🔗</div>
                            <div class="feature-title">API集成</div>
                            <div class="feature-description">与GStudios API深度集成，实现实时数据同步</div>
                        </div>
                    </div>

                    <!-- 快速开始指引 -->
                    <div class="quick-start">
                        <h3 class="quick-start-title">
                            <span>🚀</span>
                            快速开始指引
                        </h3>
                        <div class="quick-start-steps">
                            <div class="step-item">
                                <div class="step-number">1</div>
                                <div class="step-text">配置API Token</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-text">选择目标书籍</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-text">上传Excel文件</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">4</div>
                                <div class="step-text">执行智能分配</div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统状态仪表板 -->
                    <div class="status-dashboard">
                        <div class="status-card">
                            <div class="status-icon info" id="welcomeApiStatusIcon">🔗</div>
                            <div class="status-label">API连接状态</div>
                            <div class="status-value" id="welcomeApiStatus">检查中...</div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon info" id="welcomeBookStatusIcon">📚</div>
                            <div class="status-label">当前书籍</div>
                            <div class="status-value" id="welcomeBookStatus">未选择</div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon info" id="welcomeFileStatusIcon">📄</div>
                            <div class="status-label">Excel文件</div>
                            <div class="status-value" id="welcomeFileStatus">未上传</div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon info" id="welcomeTaskStatusIcon">⚡</div>
                            <div class="status-label">任务状态</div>
                            <div class="status-value" id="welcomeTaskStatus">就绪</div>
                        </div>
                    </div>

                    <!-- 快速操作按钮 -->
                    <div class="welcome-actions">
                        <button class="welcome-btn" onclick="switchTab('settings')">
                            <span>⚙️</span>
                            开始配置
                        </button>
                        <button class="welcome-btn secondary" onclick="switchTab('mapping')">
                            <span>🎭</span>
                            角色映射
                        </button>
                        <button class="welcome-btn secondary" onclick="switchTab('assignment')">
                            <span>⚡</span>
                            执行分配
                        </button>
                    </div>
                    </div> <!-- 关闭 welcome-panel-content -->
                </div>
            </div>

            <!-- 角色-CV映射标签页 -->
            <div id="mappingTab" class="tab-content">
                <!-- 映射结果显示区域 -->
                <div class="config-panel mapping-panel">
                    <div class="mapping-header">
                        <h3>🎯 角色-CV映射表</h3>

                        <!-- 动态切换的按钮组 -->
                        <div class="mapping-actions">
                            <!-- 文件选择按钮（初始状态显示） -->
                            <button id="selectFileBtn" class="btn btn-primary" onclick="selectExcelFileDialog()">
                                📂 选择Excel文件
                            </button>

                            <!-- 操作按钮组（解析成功后显示） -->
                            <div id="actionButtonsGroup" class="action-buttons-group hidden">
                                <button class="btn btn-success" onclick="exportMappingData()">
                                    📤 导出映射数据
                                </button>
                                <button class="btn btn-warning" onclick="clearMappingData()">
                                    🗑️ 清空映射数据
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文件信息显示区域 -->
                    <div id="mappingFileInfo" class="file-info-section hidden">
                        <div class="file-info-content">
                            <span class="file-info-label">已选择文件:</span>
                            <span id="mappingFileName" class="file-name"></span>
                            <span class="file-path">(<span id="mappingFilePath"></span>)</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <span id="mappingStatus" style="color: #666;"></span>
                    </div>

                    <!-- 映射表格容器 -->
                    <div class="mapping-container">
                        <div id="mappingTableContainer" class="mapping-table-wrapper">
                            <p>请先上传并解析Excel文件</p>
                        </div>

                        <!-- 分页控件将通过JavaScript动态插入到这里 -->
                        <div id="mappingPaginationContainer" class="mapping-pagination-fixed"></div>
                    </div>
                </div>
            </div>

            <!-- CV列表标签页 -->
            <div id="cvlistTab" class="tab-content">
                <div class="config-panel cv-list-panel">
                    <!-- CV列表页面头部 -->
                    <div class="cv-list-header">
                        <h3>🎤 CV列表</h3>
                        <div class="cv-list-actions">
                            <button class="btn btn-primary" onclick="loadCvList()">刷新</button>
                        </div>
                    </div>

                    <!-- CV列表表格容器 -->
                    <div class="cv-list-container">
                        <div id="cvListTableContainer" class="cv-list-table-wrapper">
                            <p>请先选择书籍并加载书籍数据</p>
                        </div>

                        <!-- 分页控件将通过JavaScript动态插入到这里 -->
                        <div id="cvListPaginationContainer" class="cv-list-pagination-fixed"></div>
                    </div>
                </div>
            </div>

            <!-- CV简名管理标签页 -->
            <div id="nicknamesTab" class="tab-content">
                <div class="config-panel nicknames-panel">
                    <!-- CV简名管理页面头部 -->
                    <div class="nicknames-header">
                        <h3>📝 CV简名管理</h3>
                        <div class="nicknames-actions">
                            <button class="btn btn-primary" onclick="loadCvNicknames()">刷新列表</button>
                        </div>
                    </div>

                    <!-- CV简名输入区域 -->
                    <div class="nicknames-input-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="cvNickname">CV简名:</label>
                                <input type="text" id="cvNickname" placeholder="输入CV简名">
                            </div>
                            <div class="form-group">
                                <label for="cvFullname">CV全名:</label>
                                <input type="text" id="cvFullname" placeholder="输入CV全名">
                            </div>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="addCvNickname()">添加映射</button>
                        </div>
                    </div>

                    <!-- CV简名列表区域 -->
                    <div class="nicknames-container">
                        <div class="nicknames-content-wrapper">
                            <div id="nicknamesTableContainer">
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分配任务标签页 -->
            <div id="assignmentTab" class="tab-content">
                <div class="config-panel assignment-panel">
                    <!-- 分配任务页面头部 -->
                    <div class="assignment-header">
                        <h3>⚡ 分配任务</h3>
                    </div>

                    <!-- 任务配置区域 -->
                    <div class="assignment-config-section">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableRealAssignment"> 启用真实分配（取消勾选为测试模式）
                            </label>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="startAssignment()">开始分配</button>
                            <button class="btn btn-warning" onclick="stopAssignment()">停止分配</button>
                        </div>
                        <div id="progressContainer" class="hidden">
                            <h4>分配进度</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                            </div>
                            <p id="progressText">准备中...</p>
                        </div>
                    </div>

                    <!-- 分配结果区域 -->
                    <div class="assignment-container">
                        <div class="assignment-content-wrapper">
                            <div id="assignmentResults" class="hidden">
                                <h4>分配结果</h4>
                                <div id="resultsContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作日志标签页 -->
            <div id="logsTab" class="tab-content">
                <div class="config-panel logs-panel">
                    <!-- 操作日志页面头部 -->
                    <div class="logs-header">
                        <h3>📋 操作日志</h3>
                        <div class="logs-actions">
                            <button class="btn btn-primary" onclick="clearLogs()">清空日志</button>
                        </div>
                    </div>

                    <!-- 操作日志容器 -->
                    <div class="logs-container">
                        <div class="logs-content-wrapper">
                            <div class="log-panel" id="logPanel">
                                <p>系统启动...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础设置标签页 -->
            <div id="settingsTab" class="tab-content">
                <div class="config-panel">
                    <!-- 页面标题和操作按钮 -->
                    <div class="settings-header">
                        <h3>⚙️ 基础设置</h3>
                        <div class="settings-actions">
                            <button class="btn btn-success" onclick="saveSettings()">保存设置</button>
                            <button class="btn btn-warning" onclick="resetSettings()">重置默认</button>
                            <button class="btn btn-primary" onclick="loadSettings()">重新加载</button>
                        </div>
                    </div>

                    <!-- 设置TAB导航 -->
                    <div class="settings-tabs">
                        <div class="settings-tab active" onclick="switchSettingsTab('api')" data-tab="api">
                            <span class="tab-icon">🔗</span>
                            <span class="tab-text">API设置</span>
                        </div>
                        <div class="settings-tab" onclick="switchSettingsTab('excel')" data-tab="excel">
                            <span class="tab-icon">📊</span>
                            <span class="tab-text">EXCEL设置</span>
                        </div>
                        <div class="settings-tab" onclick="switchSettingsTab('files')" data-tab="files">
                            <span class="tab-icon">📁</span>
                            <span class="tab-text">文件路径设置</span>
                        </div>
                    </div>

                    <!-- TAB内容区域 -->
                    <div class="settings-tab-content">
                        <!-- API设置TAB -->
                        <div id="apiSettingsTab" class="settings-tab-pane active">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsApiToken">API Token:</label>
                                    <input type="password" id="settingsApiToken" placeholder="请输入GStudios API Token">
                                    <button class="btn btn-primary" onclick="applyApiToken()">应用Token</button>
                                </div>
                                <div class="form-group">
                                    <label for="settingsApiBaseUrl">API基础URL:</label>
                                    <input type="text" id="settingsApiBaseUrl" placeholder="API基础URL">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="settingsEnableRealAssignment"> 启用真实分配模式
                                    </label>
                                    <small style="color: #666; margin-left: 20px;">取消勾选为测试模式</small>
                                </div>
                            </div>
                        </div>

                        <!-- EXCEL设置TAB -->
                        <div id="excelSettingsTab" class="settings-tab-pane">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsCharacterKeyword">角色列关键词:</label>
                                    <input type="text" id="settingsCharacterKeyword" placeholder="角色列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsCvKeyword">CV列关键词:</label>
                                    <input type="text" id="settingsCvKeyword" placeholder="CV列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsPriceKeyword">价格列关键词:</label>
                                    <input type="text" id="settingsPriceKeyword" placeholder="价格列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsHeaderRow">表头行号:</label>
                                    <input type="number" id="settingsHeaderRow" placeholder="表头行号" min="1">
                                </div>
                            </div>
                        </div>

                        <!-- 文件路径设置TAB -->
                        <div id="filesSettingsTab" class="settings-tab-pane">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsCvConfigFile">CV简名配置文件路径:</label>
                                    <input type="text" id="settingsCvConfigFile" placeholder="CV简名配置文件路径">
                                </div>
                                <div class="form-group">
                                    <label for="settingsLastExcelDir">上次Excel文件目录:</label>
                                    <input type="text" id="settingsLastExcelDir" placeholder="上次Excel文件目录" readonly>
                                    <button class="btn btn-secondary" onclick="clearLastExcelDir()">清空</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-warning">
                    <div class="footer-icon">⚠️</div>
                    <div class="footer-text">
                        <p>使用前请确保已正确配置API Token和相关设置</p>
                    </div>
                </div>
                <div class="footer-tip">
                    <div class="footer-icon">💡</div>
                    <div class="footer-text">
                        <p>建议在分配前先测试少量角色，确认配置正确后再进行批量操作</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 关于模态框 -->
    <div class="modal-overlay" id="aboutModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎭 关于角色CV分配工具</h2>
                <button class="modal-close" onclick="hideAboutModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="about-info">
                    <div class="about-section">
                        <h3>📋 应用信息</h3>
                        <p><strong>版本:</strong> v2.0.0</p>
                        <p><strong>功能:</strong> 智能化角色配音分配管理系统</p>
                        <p><strong>特色:</strong> 现代化UI设计，支持批量操作和智能匹配</p>
                    </div>

                    <div class="about-section">
                        <h3>🎨 设计特色</h3>
                        <div class="tech-stack">
                            <span class="tech-item">毛玻璃效果</span>
                            <span class="tech-item">渐变色彩</span>
                            <span class="tech-item">流畅动画</span>
                            <span class="tech-item">响应式设计</span>
                        </div>
                    </div>

                    <div class="about-section">
                        <h3>⚠️ 使用说明</h3>
                        <p><strong>配置要求:</strong> 请确保已正确配置API Token和相关设置</p>
                        <p><strong>操作建议:</strong> 建议先测试少量角色，确认配置正确后再进行批量操作</p>
                        <p><strong>数据安全:</strong> 所有操作均在本地进行，请注意数据备份</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer"></div>

    <script src="js/cv_assignment.js"></script>

    <script>
        // 模态框控制函数
        function showAboutModal() {
            const modal = document.getElementById('aboutModal');
            modal.style.display = 'flex';
            modal.classList.add('show');
        }

        function hideAboutModal() {
            const modal = document.getElementById('aboutModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 点击模态框背景关闭
        document.getElementById('aboutModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAboutModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideAboutModal();
            }
        });

        // 侧边栏控制功能
        function initSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mainContent = document.querySelector('.main-content');

            // 检测是否为移动端
            function isMobile() {
                return window.innerWidth <= 768;
            }

            // 从localStorage读取侧边栏状态
            function loadSidebarState() {
                try {
                    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (isCollapsed && !isMobile()) {
                        sidebar.classList.add('collapsed');
                        if (mainContent) {
                            mainContent.classList.add('sidebar-collapsed');
                        }
                        console.log('侧边栏状态已恢复：折叠');
                    } else {
                        console.log('侧边栏状态已恢复：展开');
                    }
                } catch (error) {
                    console.warn('读取侧边栏状态失败:', error);
                }
            }

            // 保存侧边栏状态
            function saveSidebarState(collapsed) {
                try {
                    localStorage.setItem('sidebarCollapsed', collapsed);
                    console.log('侧边栏状态已保存:', collapsed ? '折叠' : '展开');
                } catch (error) {
                    console.warn('保存侧边栏状态失败:', error);
                }
            }

            // 初始化侧边栏状态
            loadSidebarState();

            // 桌面端切换侧边栏状态
            sidebarToggle.addEventListener('click', function() {
                if (isMobile()) {
                    // 移动端关闭侧边栏
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                } else {
                    // 桌面端折叠/展开
                    sidebar.classList.toggle('collapsed');
                    if (mainContent) {
                        mainContent.classList.toggle('sidebar-collapsed');
                    }

                    // 保存状态到localStorage
                    const collapsed = sidebar.classList.contains('collapsed');
                    saveSidebarState(collapsed);

                    // 使用CSS类而非直接设置样式
                    const toggleIcon = sidebarToggle.querySelector('.toggle-icon');
                    if (toggleIcon) {
                        toggleIcon.classList.toggle('js-collapsed', collapsed);
                        toggleIcon.classList.add('js-animating');
                        setTimeout(() => {
                            toggleIcon.classList.remove('js-animating');
                        }, 150);
                    }
                }
            });

            // 移动端菜单按钮
            mobileMenuBtn.addEventListener('click', function() {
                sidebar.classList.add('mobile-open');
                sidebarOverlay.classList.add('show');
            });

            // 点击遮罩层关闭侧边栏
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');
                });
            }

            // 添加滑动手势支持（移动端）
            let touchStartX = 0;
            let touchStartY = 0;
            let isSwiping = false;

            function handleTouchStart(e) {
                if (!isMobile()) return;
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
                isSwiping = true;
            }

            function handleTouchMove(e) {
                if (!isMobile() || !isSwiping) return;

                const touchCurrentX = e.touches[0].clientX;
                const touchCurrentY = e.touches[0].clientY;
                const deltaX = touchCurrentX - touchStartX;
                const deltaY = touchCurrentY - touchStartY;

                // 只处理水平滑动
                if (Math.abs(deltaY) > Math.abs(deltaX)) {
                    isSwiping = false;
                    return;
                }

                // 从左边缘向右滑动打开侧边栏
                if (touchStartX < 20 && deltaX > 50 && !sidebar.classList.contains('mobile-open')) {
                    e.preventDefault();
                    sidebar.classList.add('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('show');
                    }
                    isSwiping = false;
                }

                // 在侧边栏上向左滑动关闭
                if (sidebar.classList.contains('mobile-open') && deltaX < -50) {
                    e.preventDefault();
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    isSwiping = false;
                }
            }

            function handleTouchEnd() {
                isSwiping = false;
            }

            // 添加触摸事件监听器
            document.addEventListener('touchstart', handleTouchStart, { passive: false });
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd);

            // 窗口大小变化处理
            function handleResize() {
                if (isMobile()) {
                    // 移动端：关闭侧边栏并移除桌面端的折叠状态
                    sidebar.classList.remove('mobile-open', 'collapsed');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    if (mainContent) {
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                } else {
                    // 桌面端：恢复保存的折叠状态
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    loadSidebarState();
                }
            }

            // 添加窗口大小变化监听器
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(handleResize, 150);
            });

            // 窗口大小改变时的处理
            window.addEventListener('resize', function() {
                if (!isMobile()) {
                    // 桌面端时移除移动端相关类
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');

                    // 恢复桌面端状态
                    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (isCollapsed) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('sidebar-collapsed');
                    } else {
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                } else {
                    // 移动端时移除桌面端状态
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('sidebar-collapsed');
                }
            });

            // 更新导航链接活动状态
            function updateNavActive(activeTab) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                const activeLink = document.querySelector(`[data-tab="${activeTab}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }

                // 移动端点击导航后自动关闭侧边栏
                if (isMobile()) {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');
                }
            }

            // 监听标签页切换，更新侧边栏活动状态
            window.updateSidebarActive = updateNavActive;

            // 为导航链接添加点击事件
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function() {
                    const tab = this.getAttribute('data-tab');
                    if (tab) {
                        updateNavActive(tab);
                    }
                });
            });
        }

        // 页面加载完成后初始化侧边栏
        document.addEventListener('DOMContentLoaded', function() {
            initSidebar();
        });
    </script>
</body>
</html>
