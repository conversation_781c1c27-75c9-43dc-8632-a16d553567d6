/*
 * Logs Panel - 日志面板样式
 * 包含操作日志页面的所有样式：日志容器、日志内容、滚动条等
 * 提供日志查看和管理功能的界面样式
 */

/* ==================== 操作日志页面样式 ==================== */
.logs-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.logs-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.logs-actions {
    display: flex;
    align-items: center;
}

/* 操作日志容器样式 */
.logs-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.logs-content-wrapper {
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 优化后的日志面板样式 */
.log-panel {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-sm);
    font-family: var(--font-mono);
    font-size: 0.75rem;
    line-height: 1.4;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Webkit浏览器滚动条样式 */
.log-panel::-webkit-scrollbar {
    width: 8px;
}

.log-panel::-webkit-scrollbar-track {
    background: transparent;
}

.log-panel::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background var(--transition-smooth);
}

.log-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 日志条目样式 */
.log-panel div {
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-sm);
    color: var(--text-secondary);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-panel p {
    margin: 0;
    padding: var(--spacing-sm);
    color: var(--text-secondary);
}
