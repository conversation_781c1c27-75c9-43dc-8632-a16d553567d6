"""
API服务

此模块提供了API服务，封装了对GStudiosAPI的操作。
"""
from typing import List, Tuple, Dict, Any, Optional
from ..models.character import Character
from ..models.cv import CV
from .gstudios_api import GStudiosAPI


class APIService:
    """API服务类
    
    封装了对GStudiosAPI的操作，提供了获取书籍、角色、CV列表和分配CV等方法。
    
    Attributes:
        api (GStudiosAPI): API客户端实例
    """
    
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None):
        """初始化API服务
        
        Args:
            base_url: API基础URL，默认为None
            token: 授权Token，默认为None
        """
        self.api = GStudiosAPI(base_url=base_url, token=token)
    
    def set_token(self, token: str) -> None:
        """设置API令牌
        
        Args:
            token: 授权Token
        """
        self.api.set_token(token)
    
    def get_book_list(self, finished: str = "all") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
                - "all": 获取所有书籍
                - "finished": 获取已完成的书籍
                - "unfinished": 获取未完成的书籍
                
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 书籍列表)
        """
        return self.api.get_book_list(finished)
    
    def get_character_list(self, book_id: str) -> Tuple[bool, List[Character]]:
        """获取角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            Tuple[bool, List[Character]]: (成功标志, 角色列表)
        """
        success, data = self.api.get_character_list(book_id)
        if not success:
            return False, []
        
        characters = [Character.from_dict(char) for char in data if isinstance(char, dict)]
        return True, characters
    
    def get_cv_list(self, book_id: str) -> Tuple[bool, List[Dict]]:
        """获取CV列表

        Args:
            book_id: 书籍ID

        Returns:
            Tuple[bool, List[Dict]]: (成功标志, CV原始数据列表)
        """
        success, data = self.api.get_cv_list(book_id)
        if not success:
            return False, []

        # 直接返回原始字典数据，保留所有字段信息
        cvs = [cv for cv in data if isinstance(cv, dict)]
        return True, cvs
    
    def assign_cv_to_character(self, character_id: str, cv_id: str) -> Tuple[bool, str]:
        """分配CV给角色
        
        Args:
            character_id: 角色ID
            cv_id: CV ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 成功/错误消息)
        """
        return self.api.assign_cv_to_character(character_id, cv_id)
