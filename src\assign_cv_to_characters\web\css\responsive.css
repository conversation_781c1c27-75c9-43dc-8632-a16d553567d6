/*
 * Responsive - 响应式样式
 * 包含所有媒体查询和响应式布局样式
 * 为不同屏幕尺寸提供适配的界面布局
 */

/* ==================== Sidebar Responsive Design ==================== */
@media (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .layout-main {
        flex-direction: row;
    }

    .sidebar {
        position: absolute;
        transform: translateX(-100%);
        width: 200px; /* 从240px减少到200px，保持与桌面端的比例协调 */
        z-index: 1100;
        height: 100%;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 移动端不使用折叠状态，而是使用mobile-open状态 */
    .sidebar.collapsed {
        transform: translateX(-100%);
        width: 200px;
    }

    .main-content {
        flex: 1;
        width: 100%;
    }

    /* 移动端遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1050;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-smooth);
        backdrop-filter: blur(4px); /* 添加模糊效果 */
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* 移动端导航项优化 */
    .nav-link {
        padding: var(--spacing-lg) var(--spacing-xl); /* 增大触摸区域 */
        font-size: 1rem; /* 稍微增大字体 */
    }

    .nav-icon {
        font-size: 1.4rem; /* 增大图标 */
        width: 28px;
        height: 28px;
    }
}

/* ==================== Header Responsive Design ==================== */
@media (max-width: 768px) {
    .app-header {
        height: auto;
        min-height: 60px; /* 从80px减少到60px */
        padding: var(--spacing-xs) var(--spacing-sm); /* 进一步减少移动端padding */
    }

    .mobile-menu-btn {
        display: flex;
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .header-left {
        align-items: flex-start;
        width: 100%;
    }

    .header-title-group {
        flex: 1;
        text-align: center;
    }

    .header-right {
        flex-direction: column;
        gap: var(--spacing-xs);
        width: 100%;
    }

    .book-selection-header {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-sm);
    }

    .book-select-header {
        min-width: 150px;
        max-width: 200px;
        flex: 1;
    }

    .welcome-features {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .operation-card {
        flex-direction: column;
        text-align: center;
    }

    .config-actions {
        flex-direction: column;
    }

    .link-buttons {
        flex-direction: column;
    }
}

/* ==================== Dashboard Responsive ==================== */
@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr;
    }
}

/* ==================== Footer Responsive Design ==================== */
@media (max-width: 768px) {
    .app-footer {
        height: auto;
        min-height: 80px;
        padding: var(--spacing-sm) 0;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
        padding: 0 var(--spacing-md);
    }

    .footer-warning, .footer-tip {
        padding: var(--spacing-sm) var(--spacing-md);
        gap: var(--spacing-sm);
        min-width: auto;
        max-width: none;
    }

    .footer-icon {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }

    .footer-text p {
        font-size: 0.85rem;
    }

    .main-content {
        padding: var(--spacing-xs); /* 移动端使用更小的padding */
        padding-top: var(--spacing-sm); /* 减少顶部间距 */
        padding-bottom: calc(80px + var(--spacing-sm)); /* 脚部高度 + 小间距 */
        min-height: calc(100vh - 140px); /* 调整移动端最小高度 */
    }

    /* 移动端面板优化 */
    .config-panel {
        padding: var(--spacing-sm); /* 移动端也使用0padding */
        margin-bottom: var(--spacing-sm); /* 减少面板间距 */
    }
}

/* ==================== Pagination Responsive ==================== */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .pagination-info {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-jump {
        justify-content: center;
    }

    .pagination-pages {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .pagination-page {
        min-width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }

    .pagination-jump-input {
        width: 2.5rem;
    }

    .book-selection-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
    }

    .book-controls-header {
        width: 100%;
        justify-content: center;
    }

    .book-select-header {
        min-width: 120px;
        max-width: 160px;
        font-size: 0.8rem;
    }

    .book-label {
        font-size: 0.8rem;
    }
}

/* ==================== Message Container Responsive ==================== */
@media (max-width: 768px) {
    #messageContainer {
        top: 100px; /* 移动端头部更高 */
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        width: auto;
        max-width: none;
    }
    
    .alert {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}

/* ==================== Mapping Layout Responsive ==================== */
@media (max-width: 768px) {
    .mapping-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .mapping-header h3 {
        text-align: center;
        min-width: auto;
    }

    .mapping-actions {
        width: 100%;
        justify-content: center;
    }

    .mapping-actions .btn {
        flex: 1;
        min-width: 0;
    }

    .action-buttons-group {
        width: 100%;
        justify-content: center;
    }

    .action-buttons-group .btn {
        flex: 1;
        min-width: 0;
    }

    .file-info-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .file-path {
        min-width: auto;
        word-break: break-all;
    }

    .btn-sm {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* ==================== 移动端遮罩层修复 ==================== */
@media (max-width: 768px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: var(--z-sidebar-overlay);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-smooth);
        backdrop-filter: blur(4px);
        pointer-events: none;
    }

    .app-container .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
    }

    .app-container .sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 确保侧边栏在移动端的正确定位 */
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 200px;
        transform: translateX(-100%);
        transition: transform var(--transition-smooth);
    }
}

/* 移动端侧边栏层级 */
@media (max-width: 768px) {
    .sidebar {
        z-index: var(--z-sidebar-mobile);
    }
}

/* ==================== 响应式设计优化 ==================== */
/* 操作日志响应式设计 */
@media (max-width: 768px) {
    .logs-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .logs-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .logs-panel {
        padding: var(--spacing-sm);
    }

    .log-panel {
        font-size: 0.7rem;
        padding: var(--spacing-sm);
    }

    /* 设置页面响应式设计 */
    .settings-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .settings-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .settings-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .settings-tab {
        width: 100%;
        justify-content: center;
    }

    .settings-tab-pane {
        padding: var(--spacing-sm);
    }

    .settings-section {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-sm);
    }

    .settings-section input[type="text"],
    .settings-section input[type="password"],
    .settings-section input[type="number"] {
        width: 100%;
        margin-right: 0;
        margin-bottom: var(--spacing-xs);
    }

    /* CV列表响应式设计 */
    .cv-list-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .cv-list-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* 映射表格响应式设计 */
    .mapping-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .mapping-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
