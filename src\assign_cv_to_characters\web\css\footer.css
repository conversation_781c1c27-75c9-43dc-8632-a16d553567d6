/*
 * Footer - 脚部样式
 * 包含应用底部固定脚部的所有样式：脚部容器、警告提示、版权信息等
 * 提供固定底部布局和交互效果
 */

/* ==================== Footer ==================== */
.app-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-sm) 0;
    z-index: 900;
    overflow: hidden;
    box-sizing: border-box;
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.app-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
}

/* 确保脚部在滚动时保持固定 */
.app-footer {
    /* 防止在某些浏览器中出现滚动问题 */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    align-items: center;
    position: relative;
    flex-wrap: wrap;
    height: 100%;
}

.footer-warning, .footer-tip {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-inverse);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-smooth);
    backdrop-filter: blur(10px);
    flex: 1;
    min-width: 280px;
    max-width: 450px;
}

.footer-warning:hover, .footer-tip:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.footer-icon {
    font-size: 1.3rem;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    flex-shrink: 0;
    transition: all var(--transition-smooth);
}

.footer-warning:hover .footer-icon, .footer-tip:hover .footer-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.footer-text {
    flex: 1;
}

.footer-text p {
    margin: 0;
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1.3;
    opacity: 0.95;
}

/* 原有的footer样式（保持向后兼容） */
.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    font-size: 0.875rem;
}

.footer-info {
    color: var(--text-secondary);
}

.separator {
    margin: 0 var(--spacing-sm);
}

.footer-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-link {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-color);
}
