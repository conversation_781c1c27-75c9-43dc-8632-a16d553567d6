/*
 * Components - 组件样式
 * 包含各种UI组件的样式：按钮、模态框、标签页、进度条、警告提示等
 * 提供可复用的UI组件样式库
 */

/* ==================== System Info ==================== */
.system-info {
    padding: var(--spacing-lg);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-glass);
    background: var(--bg-glass);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.info-item:hover {
    transform: translateX(5px) translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
    background: var(--bg-glass-hover);
}

.info-item:hover::before {
    transform: scaleY(1);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.info-value {
    color: var(--text-muted);
    font-family: var(--font-mono);
    font-size: 0.8rem;
    word-break: break-all;
    line-height: 1.4;
    background: rgba(102, 126, 234, 0.05);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all var(--transition-smooth);
}

.info-item:hover .info-value {
    background: rgba(102, 126, 234, 0.1);
    color: var(--text-primary);
}

/* ==================== Operations ==================== */
.operations-grid {
    display: grid;
    gap: var(--spacing-lg);
    padding: var(--spacing-sm);
}

.operation-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.operation-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
}

.operation-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
    background: var(--bg-glass-hover);
}

.operation-card:hover::before {
    transform: scaleY(1);
}

.operation-icon {
    font-size: 2rem;
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: var(--radius-lg);
    transition: all var(--transition-smooth);
}

.operation-card:hover .operation-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: scale(1.1) rotate(5deg);
}

.operation-content {
    flex: 1;
}

.operation-content h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.operation-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* ==================== Buttons ==================== */
.operation-btn {
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-button);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-smooth);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    min-width: 130px;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.operation-btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    box-shadow: var(--shadow-button);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.btn-secondary:hover {
    background: var(--secondary-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-hover) 0%, #2f855a 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* ==================== Enhanced Button Styles ==================== */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-button);
    cursor: pointer;
    margin-right: var(--spacing-sm);
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(217, 119, 6, 0.2);
}

.btn-warning:hover {
    background: #b45309;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
}

/* ==================== Results Panel ==================== */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    z-index: 1000;
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
}

.results-content {
    padding: var(--spacing-sm);
    max-height: 60vh;
    overflow-y: auto;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.close-btn:hover {
    background-color: var(--bg-tertiary);
}

/* ==================== Modal ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    backdrop-filter: var(--glass-blur);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-glass);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-smooth);
}

.modal-overlay.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-large {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-tertiary);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 70vh;
    overflow-y: auto;
}

/* ==================== About Modal ==================== */
.about-section {
    margin-bottom: var(--spacing-lg);
}

.about-section:last-child {
    margin-bottom: 0;
}

.about-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.about-section p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.link-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.link-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.link-btn:hover {
    background: var(--primary-hover);
}

.tech-stack {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tech-item {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

/* ==================== Loading ==================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: var(--glass-blur);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-spinner p {
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

/* ==================== Utility Classes ==================== */
.loading {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-lg);
}

.error {
    color: var(--error-color);
}

.success {
    color: var(--success-color);
}

.warning {
    color: var(--warning-color);
}

.text-center {
    text-align: center;
}

.hidden {
    display: none !important;
}

/* 统一的显示/隐藏控制类 */
.visible {
    display: block !important;
}

.flex-visible {
    display: flex !important;
}

.inline-visible {
    display: inline !important;
}

.inline-block-visible {
    display: inline-block !important;
}

/* ==================== Message Container ==================== */
#messageContainer {
    position: fixed;
    top: 80px; /* 头部高度 + 间距 */
    right: var(--spacing-md);
    width: 400px;
    max-width: calc(100vw - 2 * var(--spacing-md));
    z-index: 1200; /* 高于脚部(900)和头部(1000) */
    pointer-events: none; /* 允许点击穿透到下层元素 */
}

.alert {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    backdrop-filter: var(--glass-blur);
    box-shadow: var(--shadow-glass);
    pointer-events: auto; /* 恢复消息本身的点击事件 */
    animation: slideInRight 0.3s ease-out;
    transition: all var(--transition-smooth);
}

.alert:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
}

.alert-success {
    border-left: 4px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.alert-warning {
    border-left: 4px solid #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.alert-error {
    border-left: 4px solid #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.alert-info {
    border-left: 4px solid #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* 警告和提示样式增强 */
.alert {
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    margin: var(--spacing-md) 0;
    backdrop-filter: var(--glass-blur-light);
    border-left: 4px solid;
    transition: all var(--transition-smooth);
}

.alert-success {
    background: rgba(212, 237, 218, 0.9);
    color: #155724;
    border-color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-error {
    background: rgba(248, 215, 218, 0.9);
    color: #721c24;
    border-color: var(--error-color);
    border-left-color: var(--error-color);
}

.alert-warning {
    background: rgba(255, 243, 205, 0.9);
    color: #856404;
    border-color: var(--warning-color);
    border-left-color: var(--warning-color);
}

/* ==================== 标签页样式 ==================== */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-glass);
    margin-bottom: var(--spacing-lg);
    background: var(--bg-glass);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    padding: var(--spacing-sm);
}

.tab {
    padding: var(--spacing-sm);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-smooth);
    font-weight: 500;
    color: var(--text-secondary);
    position: relative;
}

.tab:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

/* ==================== 进度条样式 ==================== */
.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-sm) 0;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* ==================== Pagination Styles ==================== */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    margin-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-size-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-pages {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.pagination-page {
    min-width: 2rem;
    height: 2rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.pagination-page:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-ellipsis {
    padding: 0 var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-jump {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-jump-input {
    width: 3rem;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    text-align: center;
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.pagination-jump-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
}



/* ==================== 标签页显示修复 ==================== */
.tab-content {
    display: none !important; /* 确保默认隐藏 */
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.tab-content.active {
    display: flex !important; /* 确保激活时显示 */
}

/* ==================== Z-Index层级修复 ==================== */
.app-header {
    z-index: var(--z-header);
}

.sidebar {
    z-index: var(--z-sidebar);
}

.sidebar-overlay {
    z-index: var(--z-sidebar-overlay);
}

.modal-overlay {
    z-index: var(--z-modal);
}

.modal-backdrop {
    z-index: var(--z-modal-backdrop);
}

select {
    position: relative;
    z-index: var(--z-dropdown);
}