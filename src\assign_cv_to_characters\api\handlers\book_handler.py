"""
书籍和数据处理器

此模块提供书籍、角色、CV相关的数据获取功能。
"""
from typing import Dict, Any, List
from ...services.api_service import APIService
from ...utils.helpers import format_success_response, format_error_response


def get_books(api_service: APIService, status_filter: str = "unfinished") -> Dict[str, Any]:
    """获取书籍列表
    
    Args:
        api_service: API服务实例
        status_filter: 书籍状态过滤器 ("all", "finished", "unfinished")
        
    Returns:
        Dict[str, Any]: 包含书籍列表的响应
    """
    try:
        success, books_data = api_service.get_book_list(status_filter)
        
        if not success:
            return format_error_response(
                error="API调用失败",
                message="获取书籍列表失败"
            )
        
        # 转换为前端需要的格式
        books_dict = {}
        for book in books_data:
            if isinstance(book, dict) and 'name' in book and 'id' in book:
                books_dict[book['name']] = book['id']
        
        return format_success_response(
            data={
                "books": books_dict,
                "count": len(books_dict)
            },
            message=f"成功获取{len(books_dict)}本书籍"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取书籍列表时发生错误"
        )


def get_characters(api_service: APIService, book_id: str) -> Dict[str, Any]:
    """获取角色列表
    
    Args:
        api_service: API服务实例
        book_id: 书籍ID
        
    Returns:
        Dict[str, Any]: 包含角色列表的响应
    """
    try:
        if not book_id:
            return format_error_response(
                error="书籍ID不能为空",
                message="请先选择书籍"
            )
        
        success, characters = api_service.get_character_list(book_id)
        
        if not success:
            return format_error_response(
                error="API调用失败",
                message="获取角色列表失败"
            )
        
        # 转换为前端需要的格式
        characters_data = []
        for char in characters:
            characters_data.append({
                "id": char.id,
                "name": char.name,
                "cv_human_id": char.cv_human_id,
                "has_cv": char.cv_human_id is not None
            })
        
        return format_success_response(
            data={
                "characters": characters_data,
                "count": len(characters_data)
            },
            message=f"成功获取{len(characters_data)}个角色"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取角色列表时发生错误"
        )


def get_cvs(api_service: APIService, book_id: str) -> Dict[str, Any]:
    """获取CV列表
    
    Args:
        api_service: API服务实例
        book_id: 书籍ID
        
    Returns:
        Dict[str, Any]: 包含CV列表的响应
    """
    try:
        if not book_id:
            return format_error_response(
                error="书籍ID不能为空",
                message="请先选择书籍"
            )
        
        success, cvs_raw = api_service.get_cv_list(book_id)

        if not success:
            return format_error_response(
                error="API调用失败",
                message="获取CV列表失败"
            )

        # 直接使用API返回的原始数据，转换为前端需要的格式
        cvs_data = []
        for cv_raw in cvs_raw:
            # 使用原始API数据，包含更多字段信息
            cv_info = {
                "id": cv_raw.get("cvId", cv_raw.get("id", "")),
                "name": cv_raw.get("cvName", cv_raw.get("name", "")),
                "gender": cv_raw.get("gender", cv_raw.get("sex", "未知")),
                "price": cv_raw.get("price", cv_raw.get("unitPrice", 0)),
                "cooperation": cv_raw.get("cooperation", cv_raw.get("cooperationType", "未知")),
                "status": cv_raw.get("status", "未知")
            }
            cvs_data.append(cv_info)

        return format_success_response(
            data={
                "cvs": cvs_data,
                "count": len(cvs_data)
            },
            message=f"成功获取{len(cvs_data)}个CV"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取CV列表时发生错误"
        )


def get_book_info(api_service: APIService, book_id: str) -> Dict[str, Any]:
    """获取书籍详细信息
    
    Args:
        api_service: API服务实例
        book_id: 书籍ID
        
    Returns:
        Dict[str, Any]: 包含书籍信息的响应
    """
    try:
        if not book_id:
            return format_error_response(
                error="书籍ID不能为空",
                message="请提供书籍ID"
            )
        
        # 获取角色和CV信息
        char_result = get_characters(api_service, book_id)
        cv_result = get_cvs(api_service, book_id)
        
        if not char_result["success"] or not cv_result["success"]:
            return format_error_response(
                error="获取书籍信息失败",
                message="无法获取完整的书籍信息"
            )
        
        return format_success_response(
            data={
                "book_id": book_id,
                "characters": char_result["data"]["characters"],
                "cvs": cv_result["data"]["cvs"],
                "character_count": char_result["data"]["count"],
                "cv_count": cv_result["data"]["count"]
            },
            message="成功获取书籍信息"
        )
        
    except Exception as e:
        return format_error_response(
            error=str(e),
            message="获取书籍信息时发生错误"
        )
