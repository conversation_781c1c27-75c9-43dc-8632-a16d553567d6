"""
CV匹配逻辑模块

此模块包含与CV（配音演员）匹配相关的逻辑，从主程序中分离出来以便于测试。
"""
from typing import Dict, Tuple, Optional, List, Any


class CVMatcher:
    """CV匹配器类，负责处理CV的匹配逻辑

    此类封装了CV匹配的核心逻辑，包括直接匹配和通过简名查找全名的匹配方式。

    Attributes:
        cv_nickname_map (Dict[str, str]): CV简名到全名的映射字典
    """

    def __init__(self, cv_nickname_map: Dict[str, str]):
        """初始化CV匹配器

        Args:
            cv_nickname_map: CV简名到全名的映射字典
        """
        self.cv_nickname_map = cv_nickname_map

    def get_cv_full_name(self, cv_nickname: str) -> str:
        """从CV简名对照表中查找CV全名

        Args:
            cv_nickname: CV简名

        Returns:
            CV全名，如果找不到对应的全名，返回原名
        """
        nickname = cv_nickname.strip()
        if nickname in self.cv_nickname_map:
            return self.cv_nickname_map[nickname]
        return cv_nickname

    def match_cv(self, cv_name: str, cv_map: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
        """匹配CV

        尝试通过多种方式匹配CV，包括直接匹配和通过简名查找全名。

        Args:
            cv_name: 要匹配的CV名称
            cv_map: CV名称到ID的映射字典

        Returns:
            Tuple[Optional[str], Optional[str]]:
                - 第一个元素是匹配到的CV ID，如果未匹配到则为None
                - 第二个元素是匹配信息，如果是通过简名匹配则包含简名到全名的映射信息，否则为None
        """
        # 处理特殊情况：CV名为"—"表示未分配CV
        if cv_name == "—":
            return None, "未分配CV"

        # 步骤1: 直接匹配CV名称
        if cv_name in cv_map:
            # 确保返回字符串类型的cv_id
            return str(cv_map[cv_name]), None

        # 步骤2: 通过简名对照表查找全名
        cv_full_name = self.get_cv_full_name(cv_name)

        # 步骤3: 检查全名是否在CV列表中
        if cv_full_name != cv_name and cv_full_name in cv_map:
            # 确保返回字符串类型的cv_id
            return str(cv_map[cv_full_name]), f"{cv_name} -> {cv_full_name}"

        return None, None

    def batch_match_cvs(self, character_cv_map: Dict[str, str],
                        character_map: Dict[str, str],
                        cv_map: Dict[str, str]) -> Dict[str, Any]:
        """批量匹配角色和CV

        对多个角色-CV对进行匹配，并返回匹配结果统计。

        Args:
            character_cv_map: 角色名称到CV名称的映射
            character_map: 角色名称到角色ID的映射
            cv_map: CV名称到CV ID的映射

        Returns:
            Dict[str, Any]: 包含以下键的结果字典：
                - 'success_matches': 成功匹配的列表，每项包含角色名、角色ID、CV名和CV ID
                - 'unmatched_characters': 未找到的角色列表
                - 'unmatched_cvs': 未找到的CV列表
                - 'cv_nickname_matches': 通过简名匹配成功的CV列表
        """
        results = {
            'success_matches': [],
            'unmatched_characters': [],
            'unmatched_cvs': [],
            'cv_nickname_matches': []
        }

        for character_name, cv_name in character_cv_map.items():
            # 检查角色是否存在
            if character_name not in character_map:
                results['unmatched_characters'].append(character_name)
                continue

            character_id = character_map[character_name]

            # 匹配CV
            cv_id, match_info = self.match_cv(cv_name, cv_map)

            if cv_id is not None:
                # 成功匹配
                match_data = {
                    'character_name': character_name,
                    'character_id': character_id,
                    'cv_name': cv_name,
                    'cv_id': cv_id
                }
                results['success_matches'].append(match_data)

                # 如果是通过简名匹配的，记录到简名匹配列表
                if match_info and " -> " in match_info:
                    results['cv_nickname_matches'].append(match_info)
            else:
                # 未匹配到CV
                if match_info != "未分配CV":  # 排除"—"这种明确表示未分配的情况
                    results['unmatched_cvs'].append(cv_name)

        return results
