/*
 * Animations - 动画效果
 * 包含所有动画关键帧定义、过渡效果、悬停动画等
 * 提供丰富的交互动画效果
 */

/* ==================== Keyframe Animations ==================== */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ==================== JavaScript动画状态修复 ==================== */
.toggle-icon {
    transition: transform var(--transition-smooth);
    display: inline-block;
    line-height: 1;
    transform-origin: center;
}

.app-container .toggle-icon.js-animating {
    transform: scale(1.2);
}

.app-container .toggle-icon.js-collapsed {
    transform: rotate(180deg);
}

.app-container .toggle-icon.js-collapsed.js-animating {
    transform: rotate(180deg) scale(1.2);
}

/* 按钮悬停效果优化 */
.app-container .btn-refresh:hover .toggle-icon {
    transform: rotate(180deg);
}

/* ==================== 侧边栏折叠修复 ==================== */
/* 注意：主要的折叠样式已在前面定义，这里只保留必要的补充样式 */
.app-container .sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-sm);
    margin: var(--spacing-xs);
    width: 52px;
    height: 48px;
    gap: 0;
    border-radius: var(--radius-md);
}

/* ==================== 固定Header Enhancements ==================== */
/* 确保固定头部在所有浏览器中正常工作 */
.app-header {
    /* 增强固定定位的兼容性 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

/* 为可能的长内容区域添加额外的滚动优化 */
.tab-content {
    scroll-margin-top: 100px; /* 确保锚点跳转时不被头部遮挡 */
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 允许flex子项收缩 */
    overflow: hidden;
}

/* 确保模态框在固定头部之上 */
.modal {
    z-index: 1100;
}

.modal-backdrop {
    z-index: 1050;
}

/* 为操作日志等可能很长的内容添加优化 */
.log-container {
    scroll-margin-top: 100px;
}
