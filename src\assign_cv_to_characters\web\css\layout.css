/*
 * Layout - 布局容器样式
 * 包含应用主要布局结构：应用容器、中部布局、主内容区域
 * 定义整体页面布局和结构
 */

/* ==================== Layout ==================== */
.app-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 中部布局容器 - 包含侧边栏和主内容区域 */
.layout-main {
    position: fixed;
    top: 60px; /* 头部高度 */
    bottom: 80px; /* 脚部高度 */
    left: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    z-index: 100;
}

/* ==================== Main Content ==================== */
.main-content {
    flex: 1;
    padding: var(--spacing-sm);
    padding-top: var(--spacing-md); /* 减少顶部间距 */
    padding-bottom: var(--spacing-md); /* 脚部高度 + 适当间距 */
    overflow: hidden;
    height: 100%;
    box-sizing: border-box;
    transition: all var(--transition-smooth);
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 140px); /* 调整最小高度 */
}

/* 侧边栏折叠状态下主内容区域不需要特殊调整，因为使用flex布局自动处理 */
