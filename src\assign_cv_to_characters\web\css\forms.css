/*
 * Forms - 表单样式
 * 包含所有表单控件的样式：输入框、选择框、表单组、表单行等
 * 提供统一的表单控件外观和交互效果
 */

/* ==================== 表单样式 ==================== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
}

/* 表单行样式 */
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}

.form-row .form-group input,
.form-row .form-group select {
    width: 100%;
}

/* 通用select option样式 - 确保所有下拉菜单都有良好的对比度 */
select option {
    background-color: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1a202c);
    padding: 8px 12px;
    border: none;
    font-size: inherit;
}

select option:hover,
select option:focus {
    background-color: var(--bg-secondary, #f7fafc);
    color: var(--text-primary, #1a202c);
}

select option:checked {
    background-color: var(--primary-color, #667eea);
    color: #ffffff;
    font-weight: 600;
}

/* 确保下拉菜单在固定头部之下但在其他内容之上 */
select {
    position: relative;
    z-index: 10;
}

/* 输入框状态样式 */
.input-error {
    color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.input-success {
    color: #059669 !important;
    border-color: #059669 !important;
}

/* ==================== 文件上传样式 ==================== */
.file-upload {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-smooth);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur-light);
    position: relative;
    overflow: hidden;
}

.file-upload::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.8s ease;
}

.file-upload:hover {
    border-color: var(--primary-color);
    background: var(--bg-glass-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.file-upload:hover::before {
    left: 100%;
}

.file-upload.dragover {
    border-color: var(--primary-color);
    background: var(--bg-glass-hover);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}
