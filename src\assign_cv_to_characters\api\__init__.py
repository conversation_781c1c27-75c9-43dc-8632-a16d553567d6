"""
角色CV分配工具 - API Module

API模块提供前后端交互的核心接口。
包含核心API类和各种业务处理器。

模块结构：
- core.py: 核心API类，提供前端可调用的方法
- handlers/: 业务逻辑处理器目录
  - __init__.py: 处理器模块导出
  - book_handler.py: 书籍数据处理器
  - file_handler.py: 文件处理器
  - cv_handler.py: CV管理处理器
  - assignment_handler.py: 分配任务处理器
  - config_handler.py: 配置管理处理器

设计原则：
1. 所有前端可调用的方法都在core.py中定义
2. 复杂的业务逻辑分离到handlers中
3. 统一的返回格式和错误处理
4. 支持异步操作和状态管理

使用方法：
    from assign_cv_to_characters.api.core import AssignCvToCharactersAPI

    api = AssignCvToCharactersAPI()
    result = api.some_method()
"""

from .core import AssignCvToCharactersAPI

__all__ = [
    "AssignCvToCharactersAPI",
]
