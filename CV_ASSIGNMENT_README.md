# 角色CV分配工具

智能化角色配音分配管理系统，基于 pywebview 的现代化桌面应用

## 🎭 功能特性

- **智能角色CV分配**: 自动化角色配音分配管理
- **Excel数据处理**: 支持从Excel文件导入角色-CV映射关系
- **CV简名管理**: 支持CV昵称到全名的映射管理
- **API集成**: 与GStudios平台API无缝集成
- **实时进度监控**: 分配任务进度实时反馈
- **现代化界面**: 基于Web技术的响应式用户界面
- **安全可靠**: 支持测试模式和正式模式切换
- **跨平台支持**: Windows、macOS、Linux全平台兼容

## 📋 主要功能模块

### 1. 基础配置
- API Token管理
- 书籍选择和数据加载
- 配置参数管理

### 2. 文件处理
- Excel文件上传和解析
- 角色-CV映射数据提取
- 文件格式验证

### 3. CV管理
- CV简名到全名的映射管理
- 批量CV匹配和验证
- CV数据同步

### 4. 分配任务
- 自动化角色CV分配
- 实时进度监控
- 分配结果统计和报告

## 🚀 快速开始

### 环境要求

- Python 3.8+
- pip 或 uv 包管理器

### 安装依赖

使用 pip：
```bash
pip install -r requirements.txt
```

使用 uv（推荐）：
```bash
uv sync
```

### 运行应用

```bash
python -m assign_cv_to_characters
```

或使用 uv：
```bash
uv run python -m assign_cv_to_characters
```

## 📖 使用指南

### 1. 初始配置

1. 启动应用后，首先在"基础配置"面板中设置API Token
2. 点击"设置Token"按钮保存配置
3. 点击"刷新书籍"获取可用书籍列表
4. 选择要处理的书籍，点击"加载书籍数据"

### 2. 准备Excel文件

Excel文件应包含以下列：
- 角色名称列（默认第1列）
- CV名称列（默认第7列）
- 价格列（可选，默认第3列）

文件格式示例：
```
角色名称    | 其他信息 | 价格 | ... | CV名称
张三        | ...      | 100  | ... | 小明
李四        | ...      | 150  | ... | 小红
```

### 3. 上传和解析文件

1. 在"Excel文件处理"面板中，点击或拖拽Excel文件到上传区域
2. 选择文件后，点击"解析文件"按钮
3. 系统会自动解析文件并显示角色-CV映射表

### 4. 管理CV简名

1. 切换到"CV简名管理"标签页
2. 添加CV简名到全名的映射关系
3. 这有助于系统正确匹配Excel中的简化CV名称

### 5. 执行分配任务

1. 切换到"分配任务"标签页
2. 选择是否启用"真实分配"（取消勾选为测试模式）
3. 点击"开始分配"按钮
4. 监控分配进度和查看结果

## 🔧 配置说明

### API配置
- `base_url`: GStudios API基础URL
- `default_token`: 默认API Token
- `enable_real_assignment`: 是否启用真实分配

### Excel配置
- `character_sheet_name`: 角色数据所在工作表名称
- `character_column_index`: 角色名称列索引
- `cv_column_index`: CV名称列索引
- `header_row`: 表头行号

### 文件配置
- `cv_config_file`: CV简名配置文件路径
- `last_excel_directory`: 上次打开Excel文件的目录

## 🛠️ 开发说明

### 项目结构
```
assign-cv-to-characters/
├── src/assign_cv_to_characters/
│   ├── api/                    # API层
│   │   ├── core.py            # 核心API类
│   │   └── handlers/          # 业务处理器
│   ├── services/              # 服务层
│   │   ├── api_service.py     # API服务
│   │   ├── cv_service.py      # CV服务
│   │   └── config_service.py  # 配置服务
│   ├── models/                # 数据模型
│   │   ├── character.py       # 角色模型
│   │   └── cv.py              # CV模型
│   ├── utils/                 # 工具模块
│   │   ├── excel_handler.py   # Excel处理
│   │   └── helpers.py         # 辅助函数
│   ├── web/                   # 前端资源
│   │   ├── cv_assignment.html # 主界面
│   │   └── js/cv_assignment.js # 前端逻辑
│   └── main.py                # 程序入口
├── config/                    # 配置文件
├── requirements.txt           # 依赖列表
└── README.md                  # 说明文档
```

### 架构设计

- **API层**: 提供前端调用的统一接口
- **服务层**: 封装业务逻辑和外部API调用
- **模型层**: 定义数据结构和对象模型
- **工具层**: 提供通用工具函数
- **前端层**: 基于Web技术的用户界面

## 📝 注意事项

1. **测试模式**: 建议首次使用时启用测试模式，确认映射关系正确后再使用正式模式
2. **数据备份**: 执行正式分配前，建议备份重要数据
3. **网络连接**: 确保网络连接正常，能够访问GStudios API
4. **文件格式**: Excel文件格式应符合预期的列结构
5. **权限管理**: 确保API Token具有足够的权限执行分配操作

## 🐛 故障排除

### 常见问题

1. **API连接失败**: 检查网络连接和Token是否正确
2. **Excel解析失败**: 检查文件格式和列结构
3. **CV匹配失败**: 检查CV简名映射是否完整
4. **分配失败**: 检查角色和CV是否存在于系统中

### 日志查看

应用运行时会在"操作日志"标签页中显示详细的操作日志，有助于诊断问题。

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发团队
