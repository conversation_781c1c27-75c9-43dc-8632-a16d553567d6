"""
CV数据模型

此模块定义了CV(配音演员)的数据模型。
"""
from typing import Dict, Any


class CV:
    """CV数据模型
    
    表示一个配音演员(CV)及其属性，包括ID和名称。
    
    Attributes:
        id (str): CV ID
        name (str): CV名称
    """
    
    def __init__(self, id: str, name: str):
        """初始化CV对象
        
        Args:
            id: CV ID
            name: CV名称
        """
        self.id = id
        self.name = name
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CV':
        """从API返回的字典创建CV对象
        
        Args:
            data: 包含CV数据的字典
            
        Returns:
            CV: 创建的CV对象
        """
        return cls(
            id=data.get('cvId', ''),
            name=data.get('cvName', '')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 表示CV的字典
        """
        return {
            'cvId': self.id,
            'cvName': self.name
        }
    
    def __str__(self) -> str:
        """字符串表示
        
        Returns:
            str: CV的字符串表示
        """
        return f"CV(id={self.id}, name={self.name})"
