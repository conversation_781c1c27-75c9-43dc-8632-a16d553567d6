"""
角色CV分配工具 - API Handlers

业务逻辑处理器模块。
包含各种具体的业务操作实现。

设计原则：
1. 每个处理器负责特定的业务领域
2. 处理器之间保持独立，避免循环依赖
3. 统一的输入输出格式
4. 完善的错误处理和日志记录

处理器列表：
- config_handler: 配置管理处理器
- book_handler: 书籍和数据处理器
- file_handler: 文件操作处理器
- assignment_handler: 分配任务处理器
- cv_handler: CV管理处理器

使用方法：
    from assign_cv_to_characters.api.handlers import get_books

    result = get_books(params)
"""

# 导入所有处理器函数
from .config_handler import (
    load_config,
    save_config,
    get_default_config,
    validate_config,
)

# 其他处理器将在后续创建时导入
__all__ = [
    'load_config',
    'save_config',
    'get_default_config',
    'validate_config',
]

# 可选的处理器（根据需要取消注释）
# from .file_handler import (
#     read_file,
#     write_file,
#     list_directory,
# )
# from .network_handler import (
#     make_request,
#     download_file,
# )

# 导出所有可用的处理器函数
__all__ = [
    # 示例处理器
    "example_operation",
    
    # 配置管理处理器
    "load_config",
    "save_config", 
    "get_default_config",
    "validate_config",
    
    # 可选处理器（根据需要取消注释）
    # "read_file",
    # "write_file",
    # "list_directory",
    # "make_request",
    # "download_file",
]
