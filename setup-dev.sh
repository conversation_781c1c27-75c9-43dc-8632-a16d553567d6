#!/bin/bash
# Modern GUI App Template - Development Environment Setup
# 基于UV包管理器的开发环境快速设置脚本

set -e  # 遇到错误时退出

echo "🚀 Modern GUI App - 开发环境设置"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查操作系统
OS="unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
fi

echo -e "${BLUE}📋 检测到操作系统: $OS${NC}"

# 检查Python版本
check_python() {
    echo -e "${BLUE}🐍 检查Python版本...${NC}"
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ 错误: 未找到Python${NC}"
        echo -e "${YELLOW}💡 请先安装Python 3.10或更高版本${NC}"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python版本: $PYTHON_VERSION${NC}"
    
    # 检查Python版本是否满足要求
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 10 ]); then
        echo -e "${RED}❌ 错误: Python版本过低，需要3.10或更高版本${NC}"
        exit 1
    fi
}

# 安装UV
install_uv() {
    echo -e "${BLUE}📦 检查UV包管理器...${NC}"
    
    if command -v uv &> /dev/null; then
        UV_VERSION=$(uv --version 2>&1)
        echo -e "${GREEN}✅ UV已安装: $UV_VERSION${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  UV未安装，正在安装...${NC}"
    
    if [[ "$OS" == "windows" ]]; then
        echo -e "${YELLOW}💡 Windows用户请手动安装UV:${NC}"
        echo -e "${YELLOW}   powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"${NC}"
        echo -e "${YELLOW}   或访问: https://github.com/astral-sh/uv${NC}"
        exit 1
    else
        # Linux/macOS
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # 重新加载shell配置
        if [ -f "$HOME/.bashrc" ]; then
            source "$HOME/.bashrc"
        elif [ -f "$HOME/.zshrc" ]; then
            source "$HOME/.zshrc"
        fi
        
        # 检查安装是否成功
        if command -v uv &> /dev/null; then
            UV_VERSION=$(uv --version 2>&1)
            echo -e "${GREEN}✅ UV安装成功: $UV_VERSION${NC}"
        else
            echo -e "${RED}❌ UV安装失败${NC}"
            echo -e "${YELLOW}💡 请手动安装UV: https://github.com/astral-sh/uv${NC}"
            exit 1
        fi
    fi
}

# 设置Python版本
setup_python() {
    echo -e "${BLUE}🐍 设置项目Python版本...${NC}"
    
    # 使用UV管理Python版本
    if command -v uv &> /dev/null; then
        echo -e "${BLUE}📥 安装Python 3.11...${NC}"
        uv python install 3.11
        
        echo -e "${BLUE}📌 固定项目Python版本...${NC}"
        uv python pin 3.11
        
        echo -e "${GREEN}✅ Python版本设置完成${NC}"
    else
        echo -e "${YELLOW}⚠️  UV不可用，跳过Python版本管理${NC}"
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📚 安装项目依赖...${NC}"
    
    if command -v uv &> /dev/null; then
        echo -e "${BLUE}🔄 使用UV同步依赖...${NC}"
        uv sync
        
        echo -e "${BLUE}🔧 安装开发依赖...${NC}"
        uv sync --extra dev
        
        echo -e "${GREEN}✅ 依赖安装完成${NC}"
    else
        echo -e "${YELLOW}⚠️  UV不可用，使用pip安装依赖...${NC}"
        
        # 创建虚拟环境
        $PYTHON_CMD -m venv venv
        
        # 激活虚拟环境
        if [[ "$OS" == "windows" ]]; then
            source venv/Scripts/activate
        else
            source venv/bin/activate
        fi
        
        # 安装依赖
        pip install -r requirements.txt
        
        echo -e "${GREEN}✅ 依赖安装完成（使用pip）${NC}"
    fi
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}🔍 验证安装...${NC}"
    
    if command -v uv &> /dev/null; then
        echo -e "${BLUE}🧪 测试UV环境...${NC}"
        uv run python --version
        
        echo -e "${BLUE}📋 列出已安装的包...${NC}"
        uv pip list
    else
        echo -e "${BLUE}🧪 测试Python环境...${NC}"
        $PYTHON_CMD --version
        
        echo -e "${BLUE}📋 列出已安装的包...${NC}"
        pip list
    fi
    
    echo -e "${GREEN}✅ 安装验证完成${NC}"
}

# 显示使用说明
show_usage() {
    echo ""
    echo -e "${GREEN}🎉 开发环境设置完成！${NC}"
    echo "=================================="
    echo ""
    echo -e "${BLUE}📖 使用说明:${NC}"
    
    if command -v uv &> /dev/null; then
        echo -e "${YELLOW}🚀 启动应用:${NC}"
        echo "   uv run python -m modern_gui_app"
        echo ""
        echo -e "${YELLOW}🔧 开发命令:${NC}"
        echo "   uv run black .              # 代码格式化"
        echo "   uv run isort .              # 导入排序"
        echo "   uv run flake8 .             # 代码检查"
        echo "   uv run pytest              # 运行测试"
        echo ""
        echo -e "${YELLOW}📦 依赖管理:${NC}"
        echo "   uv add package-name         # 添加依赖"
        echo "   uv add --dev package-name   # 添加开发依赖"
        echo "   uv remove package-name      # 移除依赖"
        echo "   uv sync                     # 同步依赖"
        echo "   uv lock --upgrade           # 升级依赖"
    else
        echo -e "${YELLOW}🚀 启动应用:${NC}"
        echo "   source venv/bin/activate    # 激活虚拟环境"
        echo "   python -m modern_gui_app    # 启动应用"
        echo ""
        echo -e "${YELLOW}💡 建议安装UV以获得更好的开发体验:${NC}"
        echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    fi
    
    echo ""
    echo -e "${BLUE}📚 更多信息:${NC}"
    echo "   README.md                   # 项目文档"
    echo "   docs/                       # 详细文档"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}开始设置开发环境...${NC}"
    echo ""
    
    check_python
    install_uv
    setup_python
    install_dependencies
    verify_installation
    show_usage
    
    echo -e "${GREEN}🎊 所有设置完成！祝您开发愉快！${NC}"
}

# 运行主函数
main "$@"
