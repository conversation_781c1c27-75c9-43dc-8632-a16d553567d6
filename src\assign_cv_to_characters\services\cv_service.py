"""
CV服务

此模块提供了CV服务，封装了对CVMatcher的操作。
"""
from typing import Dict, Tuple, Optional, List, Any
from .cv_matcher import CVMatcher
from ..models.character import Character
from ..models.cv import CV


class CVService:
    """CV服务类
    
    封装了对CVMatcher的操作，提供了CV匹配和获取CV全名等方法。
    
    Attributes:
        cv_matcher (CVMatcher): CV匹配器实例
    """
    
    def __init__(self, cv_nickname_map: Optional[Dict[str, str]] = None):
        """初始化CV服务
        
        Args:
            cv_nickname_map: CV简名到全名的映射字典，默认为None
        """
        self.cv_matcher = CVMatcher(cv_nickname_map or {})
    
    @property
    def nickname_map(self) -> Dict[str, str]:
        """获取CV简名映射

        Returns:
            Dict[str, str]: CV简名到全名的映射字典
        """
        return self.cv_matcher.cv_nickname_map

    def update_nickname_map(self, cv_nickname_map: Dict[str, str]) -> None:
        """更新CV简名映射

        Args:
            cv_nickname_map: CV简名到全名的映射字典
        """
        self.cv_matcher = CVMatcher(cv_nickname_map)
    
    def match_cv(self, cv_name: str, cv_map: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
        """匹配CV
        
        Args:
            cv_name: 要匹配的CV名称
            cv_map: CV名称到ID的映射字典
            
        Returns:
            Tuple[Optional[str], Optional[str]]: 
                - 第一个元素是匹配到的CV ID，如果未匹配到则为None
                - 第二个元素是匹配信息，如果是通过简名匹配则包含简名到全名的映射信息，否则为None
        """
        return self.cv_matcher.match_cv(cv_name, cv_map)
    
    def get_cv_full_name(self, cv_nickname: str) -> str:
        """获取CV全名
        
        Args:
            cv_nickname: CV简名
            
        Returns:
            str: CV全名，如果找不到对应的全名，返回原名
        """
        return self.cv_matcher.get_cv_full_name(cv_nickname)
    
    def batch_match_cvs(self, character_cv_map: Dict[str, str], 
                        characters: List[Character], 
                        cvs: List[CV]) -> Dict[str, Any]:
        """批量匹配角色和CV
        
        Args:
            character_cv_map: 角色名称到CV名称的映射
            characters: 角色列表
            cvs: CV列表
            
        Returns:
            Dict[str, Any]: 包含以下键的结果字典：
                - 'success_matches': 成功匹配的列表，每项包含角色名、角色ID、CV名和CV ID
                - 'unmatched_characters': 未找到的角色列表
                - 'unmatched_cvs': 未找到的CV列表
                - 'cv_nickname_matches': 通过简名匹配成功的CV列表
        """
        # 构建映射字典
        character_map = {char.name: char.id for char in characters}
        cv_map = {cv.name: cv.id for cv in cvs}
        
        return self.cv_matcher.batch_match_cvs(character_cv_map, character_map, cv_map)
