#!/usr/bin/env python3
"""
角色CV分配工具 Template - Build Script

构建脚本，用于打包应用程序为可执行文件。
基于 Augment-Code-Free 的构建流程。

主要功能：
- 使用 PyInstaller 打包应用
- 处理资源文件和图标
- 生成跨平台可执行文件
- 自动化构建流程

使用方法：
    python scripts/build.py
    python scripts/build.py --debug  # 调试模式
    python scripts/build.py --clean  # 清理构建文件
"""

import os
import sys
import shutil
import argparse
import subprocess
from pathlib import Path
import time


def get_project_root() -> Path:
    """获取项目根目录。"""
    return Path(__file__).parent.parent


def get_version() -> str:
    """从 pyproject.toml 获取版本号。"""
    project_root = get_project_root()
    pyproject_file = project_root / "pyproject.toml"
    
    if not pyproject_file.exists():
        return "0.1.0"
    
    try:
        with open(pyproject_file, "r", encoding="utf-8") as f:
            content = f.read()
            
        import re
        match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
        if match:
            return match.group(1)
    except Exception as e:
        print(f"⚠️  无法读取版本号: {e}")
    
    return "0.1.0"


def clean_build_files():
    """清理构建文件。"""
    project_root = get_project_root()
    
    # 要清理的目录和文件
    clean_paths = [
        project_root / "build",
        project_root / "dist", 
        project_root / "*.spec",
    ]
    
    print("🧹 清理构建文件...")
    
    for path in clean_paths:
        if path.name.endswith("*.spec"):
            # 处理通配符
            for spec_file in project_root.glob("*.spec"):
                try:
                    spec_file.unlink()
                    print(f"   删除: {spec_file}")
                except Exception as e:
                    print(f"   删除失败: {spec_file} - {e}")
        elif path.exists():
            try:
                if path.is_dir():
                    shutil.rmtree(path)
                else:
                    path.unlink()
                print(f"   删除: {path}")
            except Exception as e:
                print(f"   删除失败: {path} - {e}")
    
    print("✅ 构建文件清理完成")


def check_dependencies():
    """检查构建依赖。"""
    print("🔍 检查构建依赖...")
    
    try:
        import PyInstaller
        print(f"   ✅ PyInstaller: {PyInstaller.__version__}")
    except ImportError:
        print("   ❌ PyInstaller 未安装")
        print("   请运行: pip install pyinstaller")
        return False
    
    return True


def prepare_resources(project_root: Path, temp_dir: Path):
    """准备资源文件。"""
    print("📦 准备资源文件...")
    
    # 复制图标文件
    icon_files = ["app.ico", "app.icns"]
    for icon_file in icon_files:
        icon_path = project_root / icon_file
        if icon_path.exists():
            shutil.copy2(icon_path, temp_dir / icon_file)
            print(f"   复制图标: {icon_file}")
    
    # 复制Web资源
    web_src = project_root / "src" / "assign_cv_to_characters" / "web"
    if web_src.exists():
        web_dst = temp_dir / "web"
        shutil.copytree(web_src, web_dst)
        print(f"   复制Web资源: {web_src} -> {web_dst}")


def build_application(debug: bool = False):
    """构建应用程序。"""
    project_root = get_project_root()
    version = get_version()
    
    print(f"🚀 开始构建 角色CV分配工具 v{version}")
    print(f"   项目根目录: {project_root}")
    print(f"   调试模式: {'是' if debug else '否'}")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 准备构建参数
    main_script = project_root / "src" / "assign_cv_to_characters" / "main.py"
    if not main_script.exists():
        print(f"❌ 主脚本不存在: {main_script}")
        return False
    
    # 构建参数
    build_args = [
        "pyinstaller",
        str(main_script),
        "--name", "ModernGUIApp",  # 修改为您的应用名称
        "--onefile",
        "--windowed",
        "--clean",
    ]
    
    # 添加图标
    icon_path = project_root / "app.ico"
    if icon_path.exists():
        build_args.extend(["--icon", str(icon_path)])
    
    # 添加数据文件
    web_dir = project_root / "src" / "assign_cv_to_characters" / "web"
    if web_dir.exists():
        build_args.extend([
            "--add-data", f"{web_dir}{os.pathsep}assign_cv_to_characters/web"
        ])
    
    # 调试模式
    if debug:
        build_args.append("--debug=all")
        build_args.append("--console")
    else:
        build_args.append("--noconsole")
    
    # 添加隐藏导入
    hidden_imports = [
        "assign_cv_to_characters.api.core",
        "assign_cv_to_characters.api.handlers",
        "assign_cv_to_characters.utils",
    ]
    
    for module in hidden_imports:
        build_args.extend(["--hidden-import", module])
    
    # 执行构建
    print("🔨 执行 PyInstaller...")
    print(f"   命令: {' '.join(build_args)}")
    
    try:
        result = subprocess.run(build_args, cwd=project_root, check=True)
        print("✅ PyInstaller 执行成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 执行失败: {e}")
        return False
    
    # 检查输出文件
    dist_dir = project_root / "dist"
    exe_name = "ModernGUIApp.exe" if sys.platform == "win32" else "ModernGUIApp"
    exe_path = dist_dir / exe_name
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size
        print(f"✅ 构建成功!")
        print(f"   输出文件: {exe_path}")
        print(f"   文件大小: {file_size / 1024 / 1024:.1f} MB")
        
        # 创建发布目录
        create_release_package(project_root, exe_path, version)
        
        return True
    else:
        print(f"❌ 构建失败: 找不到输出文件 {exe_path}")
        return False


def create_release_package(project_root: Path, exe_path: Path, version: str):
    """创建发布包。"""
    print("📦 创建发布包...")
    
    release_dir = project_root / "release"
    release_dir.mkdir(exist_ok=True)
    
    # 生成发布文件名
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    platform = "windows" if sys.platform == "win32" else "macos" if sys.platform == "darwin" else "linux"
    release_name = f"ModernGUIApp_v{version}_{platform}_{timestamp}"
    
    if sys.platform == "win32":
        release_name += ".exe"
    
    release_path = release_dir / release_name
    
    # 复制可执行文件
    shutil.copy2(exe_path, release_path)
    
    # 创建最新版本的符号链接或副本
    latest_name = f"ModernGUIApp_latest_{platform}"
    if sys.platform == "win32":
        latest_name += ".exe"
    
    latest_path = release_dir / latest_name
    if latest_path.exists():
        latest_path.unlink()
    
    shutil.copy2(exe_path, latest_path)
    
    print(f"   发布文件: {release_path}")
    print(f"   最新版本: {latest_path}")


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description="角色CV分配工具 构建脚本")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--clean", action="store_true", help="清理构建文件")
    parser.add_argument("--clean-only", action="store_true", help="仅清理构建文件，不构建")
    
    args = parser.parse_args()
    
    if args.clean or args.clean_only:
        clean_build_files()
        if args.clean_only:
            return
    
    # 开始构建
    start_time = time.time()
    success = build_application(debug=args.debug)
    end_time = time.time()
    
    print(f"\n{'='*50}")
    if success:
        print(f"🎉 构建完成! 耗时: {end_time - start_time:.1f} 秒")
    else:
        print(f"💥 构建失败! 耗时: {end_time - start_time:.1f} 秒")
        sys.exit(1)


if __name__ == "__main__":
    main()
