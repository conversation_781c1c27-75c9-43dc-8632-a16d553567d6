/*
 * Nicknames Panel - CV简名管理面板样式
 * 包含CV简名管理页面的所有样式：面板容器、头部、输入区域、内容区域等
 * 提供CV简名管理功能的界面样式
 */

/* ==================== CV简名管理页面样式 ==================== */
.nicknames-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.nicknames-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    flex-shrink: 0;
}

.nicknames-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.nicknames-actions {
    display: flex;
    align-items: center;
}

.nicknames-input-section {
    flex-shrink: 0;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
}

.nicknames-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    width: 100%;
}

.nicknames-content-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    padding: var(--spacing-sm);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.nicknames-content-wrapper::-webkit-scrollbar {
    width: 8px;
}

.nicknames-content-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}
