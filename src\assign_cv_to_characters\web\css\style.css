/*
 * 现代化GUI应用模板 - 主样式入口点
 * 基于 Augment-Code-Free 的现代化样式设计
 *
 * 此文件作为样式的主入口点，通过 @import 导入各个功能模块的样式文件
 * 确保样式的模块化管理和良好的可维护性
 */

/* ==================== CSS导入 ==================== */
/* 按照依赖关系顺序导入样式文件 */

/* 1. 全局变量 - 必须最先导入，为其他样式提供变量支持 */
@import url('./variables.css');

/* 2. 重置样式 - 提供统一的基础样式环境 */
@import url('./reset.css');

/* 3. 布局样式 - 定义页面主要结构 */
@import url('./layout.css');

/* 4. 头部样式 - 固定头部和导航 */
@import url('./header.css');

/* 5. 侧边栏样式 - 导航菜单和折叠功能 */
@import url('./sidebar.css');

/* 6. 脚部样式 - 固定底部信息 */
@import url('./footer.css');

/* 7. 通用面板样式 - 为专用面板提供基础样式 */
@import url('./panels.css');

/* 8. 专用面板样式 - 各种功能面板 */
@import url('./welcome-panel.css');
@import url('./config-panel.css');
@import url('./logs-panel.css');
@import url('./settings-panel.css');
@import url('./nicknames-panel.css');
@import url('./assignment-panel.css');
@import url('./cv-list-panel.css');
@import url('./mapping-panel.css');

/* 9. 表单样式 - 输入控件和表单布局 */
@import url('./forms.css');

/* 10. 组件样式 - 可复用的UI组件 */
@import url('./components.css');

/* 11. 动画效果 - 过渡动画和关键帧 */
@import url('./animations.css');

/* 12. 响应式样式 - 最后导入，确保能覆盖其他样式 */
@import url('./responsive.css');

/* ==================== 拆分完成 ==================== */
/*
 * CSS文件拆分已完成！
 *
 * 原始的 style.css 文件已按功能模块拆分为15个独立的CSS文件。
 * 所有样式功能保持不变，只是重新组织了文件结构以提高可维护性。
 */
