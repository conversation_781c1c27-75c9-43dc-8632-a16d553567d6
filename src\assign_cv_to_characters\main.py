"""
角色CV分配工具 Template - Main Entry Point

基于 Augment-Code-Free 架构的应用程序入口点。
负责初始化 pywebview 应用和配置窗口属性。

主要功能：
- 创建和配置 pywebview 窗口
- 初始化 API 实例
- 处理跨平台图标设置
- 管理应用生命周期

使用方法：
    python -m assign_cv_to_characters
    或
    assign-cv-to-characters
"""

import os
import sys
import argparse
import webview
from pathlib import Path

# 添加当前目录到Python路径以支持直接运行
import sys
from pathlib import Path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # 尝试相对导入（开发环境）
    from .api.core import AssignCvToCharactersAPI
except ImportError:
    try:
        # 回退到绝对导入（打包环境）
        from assign_cv_to_characters.api.core import AssignCvToCharactersAPI
    except ImportError:
        # 最后尝试直接导入
        from api.core import AssignCvToCharactersAPI

# Windows特定导入
if sys.platform == "win32":
    try:
        import ctypes
    except ImportError:
        ctypes = None


def get_web_dir() -> str:
    """
    获取Web资源目录路径。
    
    Returns:
        str: Web目录的绝对路径
    """
    current_dir = Path(__file__).parent
    web_dir = current_dir / "web"
    return str(web_dir)


def get_icon_path() -> str | None:
    """
    获取应用程序图标路径。
    支持开发环境和PyInstaller打包环境。

    Returns:
        str | None: 图标文件路径，如果不存在则返回None
    """
    if getattr(sys, "frozen", False):
        # PyInstaller打包环境
        base_path = Path(sys._MEIPASS)
        icon_path = base_path / "app.ico"
    else:
        # 开发环境 - 图标在项目根目录
        project_root = Path(__file__).parent.parent.parent
        icon_path = project_root / "app.ico"

    # 检查文件是否存在且是真正的ICO文件（不是文本占位符）
    if icon_path.exists():
        try:
            # 检查文件是否是二进制文件（ICO文件应该是二进制的）
            with open(icon_path, 'rb') as f:
                header = f.read(4)
                # ICO文件的魔数是 00 00 01 00
                if header == b'\x00\x00\x01\x00':
                    return str(icon_path)
        except Exception:
            pass

    return None


def set_windows_icon(icon_path: str) -> None:
    """
    在Windows上使用Win32 API设置应用程序图标。
    在窗口创建后调用。
    
    Args:
        icon_path (str): 图标文件路径
    """
    if sys.platform != "win32" or not ctypes:
        return
    
    try:
        # 等待窗口创建
        import time
        time.sleep(0.5)
        
        # 获取窗口句柄
        hwnd = ctypes.windll.user32.GetForegroundWindow()
        if hwnd == 0:
            return
        
        # 加载图标
        hicon = ctypes.windll.user32.LoadImageW(
            None,  # hInst
            icon_path,  # name
            1,  # IMAGE_ICON
            0,  # cx (使用默认大小)
            0,  # cy (使用默认大小)
            0x00000010 | 0x00000040  # LR_LOADFROMFILE | LR_DEFAULTSIZE
        )
        
        if hicon != 0:
            # 设置小图标和大图标
            ctypes.windll.user32.SendMessageW(hwnd, 0x0080, 0, hicon)  # WM_SETICON, ICON_SMALL
            ctypes.windll.user32.SendMessageW(hwnd, 0x0080, 1, hicon)  # WM_SETICON, ICON_LARGE
            print("✅ Windows图标设置成功")
        else:
            print(f"❌ 图标加载失败: {icon_path}")
    
    except Exception as e:
        print(f"❌ 设置Windows图标时出错: {e}")


def window_loaded():
    """窗口加载完成后的回调函数。"""
    icon_path = get_icon_path()
    if icon_path and sys.platform == "win32":
        # 在窗口创建后设置Windows图标
        import threading
        threading.Thread(target=set_windows_icon, args=(icon_path,), daemon=True).start()


def main():
    """
    应用程序主函数。
    创建并启动Modern GUI应用。
    """
    print("🎭 启动角色CV分配工具...")
    print("=" * 50)

    try:
        # 创建API实例
        print("🔧 初始化API...")
        api = AssignCvToCharactersAPI()
        print("✅ API初始化完成")
    
        # 获取Web目录
        print("📁 检查Web资源...")
        web_dir = get_web_dir()
        print(f"   Web目录: {web_dir}")

        # 检查是否有测试模式环境变量
        test_mode = os.environ.get("TEST_FILE_PATH", "").lower() in ("1", "true", "yes")

        if test_mode:
            # 测试模式：使用文件路径测试页面
            project_root = Path(__file__).parent.parent.parent
            test_path = project_root / "test_file_path.html"
            html_path = str(test_path) if test_path.exists() else os.path.join(web_dir, "cv_assignment.html")
            print(f"🧪 测试模式：尝试加载 {test_path}")
            print(f"🧪 文件存在: {test_path.exists()}")
            print(f"🧪 最终使用: {html_path}")
        else:
            # 正常模式：使用CV分配界面
            cv_assignment_path = os.path.join(web_dir, "cv_assignment.html")
            index_path = os.path.join(web_dir, "index.html")
            html_path = cv_assignment_path if os.path.exists(cv_assignment_path) else index_path

        # 检查Web文件是否存在
        if not os.path.exists(html_path):
            print(f"❌ 错误: 在 {html_path} 找不到Web文件")
            print(f"Web目录: {web_dir}")
            print("请确保web目录包含HTML文件")
            sys.exit(1)

        print(f"📄 使用界面文件: {html_path}")
    
        # 获取图标路径
        print("🎨 检查应用图标...")
        icon_path = get_icon_path()
        if icon_path:
            print(f"   📱 使用图标: {icon_path}")
        else:
            print("   ⚠️  未找到应用图标")
    
        # 创建webview窗口配置
        print("🪟 配置应用窗口...")
        window_kwargs = {
            "title": "角色CV分配工具 v0.1.0",
            "url": html_path,
            "js_api": api,
            "width": 1400,  # 适合CV分配工具的窗口大小
            "height": 900,
            "min_size": (1000, 700),  # 最小窗口大小
            "resizable": True,
            "shadow": True,
            "on_top": False,
            "maximized": False,  # 是否最大化启动
            "minimized": False,  # 是否最小化启动
        }

        # 创建窗口
        print("🚀 创建应用窗口...")
        window = webview.create_window(**window_kwargs)
        print("✅ 窗口创建成功")
    
        # 设置窗口加载回调（用于Windows图标设置）
        if icon_path and sys.platform == "win32":
            window.events.loaded += window_loaded

        print("🌐 启动Web界面...")
        print("💡 关闭应用窗口以退出程序")
        print("=" * 50)

        # 检查是否启用调试模式
        debug_mode = os.environ.get("MODERN_GUI_DEBUG", "").lower() in ("1", "true", "yes")
        dev_mode = os.environ.get("MODERN_GUI_DEV_MODE", "").lower() in ("1", "true", "yes")

        # 只在明确指定调试或开发模式时启用debug
        enable_debug = debug_mode or dev_mode

        if enable_debug:
            print("🐛 调试模式已启用 - 开发者工具可用")
        else:
            print("🚀 生产模式启动 - 开发者工具已禁用")

        # 启动应用
        # 启动webview
        if sys.platform == "win32":
            # Windows: 不使用icon参数，手动设置
            webview.start(debug=enable_debug)
        else:
            # Linux/Mac: 使用icon参数
            start_kwargs = {"debug": enable_debug}
            if icon_path:
                start_kwargs["icon"] = icon_path
            webview.start(**start_kwargs)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断应用")
    except Exception as e:
        print(f"❌ 启动应用时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    print("👋 应用已退出")


if __name__ == "__main__":
    main()
