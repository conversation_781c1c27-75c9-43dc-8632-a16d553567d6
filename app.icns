# Modern GUI App Template - macOS Icon Placeholder
# 
# 这是macOS应用程序图标的占位符文件。
# 在实际项目中，这应该是一个二进制ICNS文件。
#
# 🍎 macOS图标特性：
#
# ICNS格式是Apple的图标格式，包含多种分辨率的图像：
# - 支持Retina显示器的高分辨率版本
# - 自动适配不同的显示环境（Dock、Finder、Spotlight等）
# - 优化的压缩算法，文件大小相对较小
#
# 📏 ICNS包含的尺寸：
#
# 标准尺寸：
# - 16x16    (icon_16x16.png)
# - 32x32    (icon_32x32.png) 
# - 128x128  (icon_128x128.png)
# - 256x256  (icon_256x256.png)
# - 512x512  (icon_512x512.png)
#
# Retina版本（@2x）：
# - 32x32    (<EMAIL>)
# - 64x64    (<EMAIL>)
# - 256x256  (<EMAIL>)
# - 512x512  (<EMAIL>)
# - 1024x1024 (<EMAIL>)
#
# 🔧 生成ICNS文件的方法：
#
# 方法1：使用模板提供的脚本（推荐）
# python scripts/generate_icons.py your_icon.png
# # 自动生成包括ICNS在内的所有平台图标
#
# 方法2：使用macOS系统工具
# 1. 创建iconset目录结构
# 2. 准备各种尺寸的PNG文件
# 3. 使用iconutil命令：
#    iconutil -c icns app.iconset
#
# 方法3：使用第三方工具
# - Image2icon (Mac App Store)
# - IconFly (免费在线工具)
# - Img2icns (命令行工具)
#
# 📋 iconset目录结构示例：
#
# app.iconset/
# ├── icon_16x16.png
# ├── <EMAIL>
# ├── icon_32x32.png
# ├── <EMAIL>
# ├── icon_128x128.png
# ├── <EMAIL>
# ├── icon_256x256.png
# ├── <EMAIL>
# ├── icon_512x512.png
# └── <EMAIL>
#
# 🎨 macOS图标设计指南：
#
# 1. 遵循Apple Human Interface Guidelines
# 2. 使用圆角矩形作为基础形状（系统会自动应用）
# 3. 避免使用系统预留的图标样式
# 4. 确保在深色和浅色背景下都清晰可见
# 5. 保持简洁，避免过多细节
#
# 🔍 质量检查：
#
# 生成ICNS文件后，可以通过以下方式检查：
# 1. 在Finder中预览图标
# 2. 使用"获取信息"查看不同尺寸
# 3. 在Dock中测试显示效果
# 4. 检查Retina显示器上的清晰度
#
# 💡 提示：
#
# - ICNS文件通常比ICO文件大，因为包含更多尺寸
# - 确保所有尺寸的图标都清晰可辨
# - 测试在不同macOS版本上的兼容性
# - 考虑深色模式下的显示效果
#
# 🚀 构建集成：
#
# 构建脚本会自动检测app.icns文件：
# - 如果存在，将其包含在macOS应用包中
# - 如果不存在，会显示警告但继续构建
# - PyInstaller会自动处理图标的打包
#
# 替换此占位符文件为真正的ICNS文件后，
# 重新运行构建脚本以应用新图标。
