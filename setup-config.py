#!/usr/bin/env python3
"""
角色CV分配工具 - 配置文件设置脚本
自动创建和配置config.json文件
"""

import json
import os
import shutil
from pathlib import Path


def main():
    """主函数"""
    print("🎭 角色CV分配工具 - 配置设置")
    print("=" * 50)
    
    # 检查是否已存在配置文件
    config_file = Path("config.json")
    if config_file.exists():
        print("⚠️  配置文件 config.json 已存在")
        choice = input("是否要重新配置？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("配置设置已取消")
            return
    
    # 复制示例配置文件
    example_file = Path("config.example.json")
    if not example_file.exists():
        print("❌ 找不到示例配置文件 config.example.json")
        return
    
    print("📋 正在创建配置文件...")
    shutil.copy(example_file, config_file)
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 配置API Token
    print("\n🔑 API配置")
    print("-" * 30)
    
    current_token = config['API']['default_token']
    if current_token == "YOUR_API_TOKEN_HERE":
        print("请输入您的GStudios API Token:")
        token = input("API Token: ").strip()
        if token:
            config['API']['default_token'] = token
            print("✅ API Token已设置")
        else:
            print("⚠️  API Token为空，您可以稍后在应用中设置")
    else:
        print(f"当前API Token: {current_token[:8]}...")
        change = input("是否要更改API Token？(y/N): ").strip().lower()
        if change in ['y', 'yes']:
            token = input("新的API Token: ").strip()
            if token:
                config['API']['default_token'] = token
                print("✅ API Token已更新")
    
    # 配置真实分配模式
    print(f"\n当前分配模式: {'真实模式' if config['API']['enable_real_assignment'] == 'true' else '测试模式'}")
    mode_choice = input("是否启用真实分配模式？(y/N): ").strip().lower()
    config['API']['enable_real_assignment'] = 'true' if mode_choice in ['y', 'yes'] else 'false'
    
    # 配置Excel设置
    print("\n📊 Excel配置")
    print("-" * 30)
    print("当前Excel配置:")
    print(f"  角色列关键词: {config['EXCEL']['character_column_keyword']}")
    print(f"  CV列关键词: {config['EXCEL']['cv_column_keyword']}")
    print(f"  价格列关键词: {config['EXCEL']['price_column_keyword']}")
    print(f"  表头行号: {config['EXCEL']['header_row']}")
    
    excel_change = input("是否要修改Excel配置？(y/N): ").strip().lower()
    if excel_change in ['y', 'yes']:
        char_keyword = input(f"角色列关键词 [{config['EXCEL']['character_column_keyword']}]: ").strip()
        if char_keyword:
            config['EXCEL']['character_column_keyword'] = char_keyword
        
        cv_keyword = input(f"CV列关键词 [{config['EXCEL']['cv_column_keyword']}]: ").strip()
        if cv_keyword:
            config['EXCEL']['cv_column_keyword'] = cv_keyword
        
        price_keyword = input(f"价格列关键词 [{config['EXCEL']['price_column_keyword']}]: ").strip()
        if price_keyword:
            config['EXCEL']['price_column_keyword'] = price_keyword
        
        header_row = input(f"表头行号 [{config['EXCEL']['header_row']}]: ").strip()
        if header_row and header_row.isdigit():
            config['EXCEL']['header_row'] = header_row
    
    # 保存配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    print("\n✅ 配置文件已保存到 config.json")
    print("\n🚀 现在您可以运行以下命令启动应用:")
    print("   python run.py")
    print("\n💡 提示:")
    print("   - config.json 包含敏感信息，不会被提交到Git")
    print("   - 您可以在应用的'基础设置'页面中修改这些配置")
    print("   - 如需重新配置，请再次运行此脚本")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  配置设置已取消")
    except Exception as e:
        print(f"\n❌ 配置设置失败: {e}")
        print("请检查文件权限或手动复制 config.example.json 为 config.json")
