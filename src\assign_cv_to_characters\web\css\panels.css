/*
 * Panels - 通用面板卡片样式
 * 包含所有面板卡片的基础样式：面板容器、面板头部、刷新按钮等
 * 为各种专用面板提供统一的基础样式
 */

/* ==================== Panel Cards ==================== */
.panel-card {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-glass);
    border: 1px solid var(--border-glass);
    overflow: hidden;
    transition: all var(--transition-smooth);
    position: relative;
}

.panel-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-glass);
    background: var(--bg-glass-secondary);
    position: relative;
}

.panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-lg);
    right: var(--spacing-lg);
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

.panel-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.refresh-btn {
    background: none;
    border: none;
    font-size: 1.125rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    color: var(--text-secondary);
}

.refresh-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: rotate(180deg);
    color: var(--primary-color);
}
