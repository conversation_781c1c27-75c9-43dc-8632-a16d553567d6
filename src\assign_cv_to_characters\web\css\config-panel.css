/*
 * Config Panel - 配置面板样式
 * 包含配置相关的所有样式：配置面板容器、配置编辑器、配置操作等
 * 提供配置管理和编辑功能的界面样式
 */

/* ==================== Config Modal ==================== */
.config-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.config-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.config-btn:hover {
    background: var(--primary-hover);
}

.config-editor {
    width: 100%;
}

#configEditor {
    width: 100%;
    height: 300px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    resize: vertical;
    background: var(--bg-secondary);
}

#configEditor:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* ==================== Config Panel Enhancements ==================== */
.config-panel {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-glass);
    box-shadow: var(--shadow-glass);
    border: 1px solid var(--border-glass);
    padding: 0; /* 从1rem减少到0rem */
    margin-bottom: var(--spacing-md); /* 从1.5rem减少到1rem */
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.config-panel:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass-hover);
    border-color: var(--border-glass-hover);
}

.config-panel h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}
