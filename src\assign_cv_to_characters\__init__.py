"""
角色CV分配工具 Template

基于 Augment-Code-Free 架构的现代GUI应用模板。
提供了完整的跨平台桌面应用开发框架。

主要特性：
- 基于 pywebview 的现代化界面
- 前后端分离的架构设计
- 跨平台兼容性支持
- 模块化的代码组织
- 统一的错误处理机制
- 完善的配置管理

使用方法：
    from assign_cv_to_characters.main import main
    main()

或者通过命令行：
    assign-cv-to-characters

作者: 角色CV分配工具开发团队
版本: 0.1.0
许可: MIT
"""

__version__ = "0.1.0"
__author__ = "角色CV分配工具开发团队"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导出主要组件
from .main import main
from .api.core import AssignCvToCharactersAPI

__all__ = [
    "main",
    "AssignCvToCharactersAPI",
    "__version__",
    "__author__",
    "__email__",
    "__license__",
]
