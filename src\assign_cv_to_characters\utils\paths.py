"""
Modern GUI App Template - Path Utilities

跨平台路径处理工具模块。
基于 Augment-Code-Free 的路径处理设计，提供统一的跨平台路径操作。

主要功能：
- 获取各种系统目录路径
- 跨平台路径处理
- 目录创建和验证
- 路径规范化

支持平台：
- Windows (win32)
- macOS (darwin)
- Linux (其他Unix-like系统)

设计特点：
- 使用 pathlib.Path 进行现代化路径操作
- 提供平台特定的路径获取
- 包含错误处理和回退机制
- 支持环境变量和用户目录
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union


def get_home_dir() -> str:
    """
    获取用户主目录路径。
    
    Returns:
        str: 用户主目录的绝对路径
    
    Examples:
        >>> home = get_home_dir()
        >>> print(home)  # Windows: C:\\Users\\<USER>\\Users\\<username>\\AppData\\Roaming)
        - macOS: ~/Library/Application Support
        - Linux: ~/.local/share
    
    Examples:
        >>> app_data = get_app_data_dir()
        >>> print(app_data)
    """
    if sys.platform == "win32":
        # Windows
        return os.getenv("APPDATA", str(Path.home() / "AppData" / "Roaming"))
    elif sys.platform == "darwin":
        # macOS
        return str(Path.home() / "Library" / "Application Support")
    else:
        # Linux和其他Unix-like系统
        return str(Path.home() / ".local" / "share")


def get_config_dir(app_name: str = "modern_gui_app") -> Path:
    """
    获取应用配置目录路径。
    
    Args:
        app_name (str): 应用名称，用于创建专用配置目录
    
    Returns:
        Path: 配置目录的Path对象
    
    平台特定路径：
        - Windows: %APPDATA%\\<app_name>
        - macOS: ~/Library/Application Support/<app_name>
        - Linux: ~/.config/<app_name>
    
    Examples:
        >>> config_dir = get_config_dir("my_app")
        >>> print(config_dir)
    """
    if sys.platform == "win32":
        # Windows
        base_dir = Path(os.getenv("APPDATA", str(Path.home() / "AppData" / "Roaming")))
    elif sys.platform == "darwin":
        # macOS
        base_dir = Path.home() / "Library" / "Application Support"
    else:
        # Linux和其他Unix-like系统
        base_dir = Path.home() / ".config"
    
    return base_dir / app_name


def get_temp_dir() -> str:
    """
    获取临时目录路径。
    
    Returns:
        str: 临时目录的绝对路径
    
    Examples:
        >>> temp_dir = get_temp_dir()
        >>> print(temp_dir)
    """
    import tempfile
    return tempfile.gettempdir()


def get_documents_dir() -> str:
    """
    获取用户文档目录路径。
    
    Returns:
        str: 文档目录的绝对路径
    
    平台特定路径：
        - Windows: %USERPROFILE%\\Documents
        - macOS: ~/Documents
        - Linux: ~/Documents
    """
    if sys.platform == "win32":
        # Windows
        documents_path = os.getenv("USERPROFILE")
        if documents_path:
            return str(Path(documents_path) / "Documents")
    
    # macOS和Linux
    return str(Path.home() / "Documents")


def get_downloads_dir() -> str:
    """
    获取用户下载目录路径。
    
    Returns:
        str: 下载目录的绝对路径
    """
    return str(Path.home() / "Downloads")


def get_desktop_dir() -> str:
    """
    获取用户桌面目录路径。
    
    Returns:
        str: 桌面目录的绝对路径
    """
    if sys.platform == "win32":
        # Windows
        desktop_path = os.getenv("USERPROFILE")
        if desktop_path:
            return str(Path(desktop_path) / "Desktop")
    
    # macOS和Linux
    return str(Path.home() / "Desktop")


def ensure_dir_exists(dir_path: Union[str, Path], create_parents: bool = True) -> bool:
    """
    确保目录存在，如果不存在则创建。
    
    Args:
        dir_path (Union[str, Path]): 目录路径
        create_parents (bool): 是否创建父目录
    
    Returns:
        bool: 目录是否存在或创建成功
    
    Examples:
        >>> success = ensure_dir_exists("/path/to/my/config")
        >>> if success:
        ...     print("目录已准备就绪")
    """
    try:
        path = Path(dir_path)
        path.mkdir(parents=create_parents, exist_ok=True)
        return True
    except (OSError, PermissionError) as e:
        print(f"创建目录失败: {dir_path}, 错误: {e}")
        return False


def normalize_path(path: Union[str, Path]) -> str:
    """
    规范化路径，处理相对路径和符号链接。
    
    Args:
        path (Union[str, Path]): 要规范化的路径
    
    Returns:
        str: 规范化后的绝对路径
    
    Examples:
        >>> normalized = normalize_path("../config/app.json")
        >>> print(normalized)
    """
    return str(Path(path).resolve())


def is_path_safe(path: Union[str, Path], base_dir: Union[str, Path]) -> bool:
    """
    检查路径是否安全（在指定的基础目录内）。
    
    Args:
        path (Union[str, Path]): 要检查的路径
        base_dir (Union[str, Path]): 基础目录
    
    Returns:
        bool: 路径是否安全
    
    Examples:
        >>> safe = is_path_safe("/home/<USER>/config/app.json", "/home/<USER>")
        >>> print(safe)  # True
        >>> safe = is_path_safe("../../../etc/passwd", "/home/<USER>")
        >>> print(safe)  # False
    """
    try:
        path_resolved = Path(path).resolve()
        base_resolved = Path(base_dir).resolve()
        
        # 检查路径是否在基础目录内
        return str(path_resolved).startswith(str(base_resolved))
    except (OSError, ValueError):
        return False


def get_file_size(file_path: Union[str, Path]) -> Optional[int]:
    """
    获取文件大小（字节）。
    
    Args:
        file_path (Union[str, Path]): 文件路径
    
    Returns:
        Optional[int]: 文件大小，如果文件不存在返回None
    
    Examples:
        >>> size = get_file_size("config.json")
        >>> if size is not None:
        ...     print(f"文件大小: {size} 字节")
    """
    try:
        return Path(file_path).stat().st_size
    except (OSError, FileNotFoundError):
        return None


def is_file_writable(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否可写。
    
    Args:
        file_path (Union[str, Path]): 文件路径
    
    Returns:
        bool: 文件是否可写
    
    Examples:
        >>> writable = is_file_writable("config.json")
        >>> if not writable:
        ...     print("文件不可写")
    """
    try:
        path = Path(file_path)
        if path.exists():
            return os.access(path, os.W_OK)
        else:
            # 如果文件不存在，检查父目录是否可写
            return os.access(path.parent, os.W_OK)
    except (OSError, PermissionError):
        return False


def get_relative_path(path: Union[str, Path], base: Union[str, Path]) -> str:
    """
    获取相对于基础路径的相对路径。
    
    Args:
        path (Union[str, Path]): 目标路径
        base (Union[str, Path]): 基础路径
    
    Returns:
        str: 相对路径
    
    Examples:
        >>> rel_path = get_relative_path("/home/<USER>/config/app.json", "/home/<USER>")
        >>> print(rel_path)  # config/app.json
    """
    try:
        return str(Path(path).relative_to(Path(base)))
    except ValueError:
        # 如果无法计算相对路径，返回绝对路径
        return str(Path(path).resolve())


def join_paths(*paths: Union[str, Path]) -> str:
    """
    安全地连接多个路径组件。
    
    Args:
        *paths: 路径组件
    
    Returns:
        str: 连接后的路径
    
    Examples:
        >>> full_path = join_paths(get_config_dir(), "app", "config.json")
        >>> print(full_path)
    """
    if not paths:
        return ""
    
    result = Path(paths[0])
    for path in paths[1:]:
        result = result / path
    
    return str(result)


def backup_file(file_path: Union[str, Path], backup_suffix: str = ".backup") -> Optional[str]:
    """
    创建文件备份。
    
    Args:
        file_path (Union[str, Path]): 要备份的文件路径
        backup_suffix (str): 备份文件后缀
    
    Returns:
        Optional[str]: 备份文件路径，失败时返回None
    
    Examples:
        >>> backup_path = backup_file("config.json")
        >>> if backup_path:
        ...     print(f"备份已创建: {backup_path}")
    """
    try:
        import shutil
        import time
        
        source_path = Path(file_path)
        if not source_path.exists():
            return None
        
        timestamp = int(time.time())
        backup_path = source_path.with_suffix(f"{backup_suffix}.{timestamp}{source_path.suffix}")
        
        shutil.copy2(source_path, backup_path)
        return str(backup_path)
    except (OSError, PermissionError):
        return None
